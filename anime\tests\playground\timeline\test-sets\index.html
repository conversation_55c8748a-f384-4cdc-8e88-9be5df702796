<!DOCTYPE html>
<html>
<head>
  <title>Timeline tests sets | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../../assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
    #test-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 2rem;
      max-width: 32rem;
    }
    #timeline-test {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2rem;
    }
    #progress-bar {
      width: 100%;
    }
    .square {
      position: relative;
      width: 5rem;
      height: 5rem;
      background-color: currentColor;
    }
    .tl-viewer {
      width: 100px;
      height: 10px;
      background-color: red;
    }
  </style>
</head>
<body>
  <div id="test-wrapper">
    <div class="tl-viewer"></div>
    <div id="timeline-test">
      <div id="square-1" class="square red"></div>
      <div id="square-2" class="square red"></div>
      <div id="square-3" class="square red"></div>
    </div>
  </div>
  <script type="module" src="./index.js"></script>
</body>
</html>
