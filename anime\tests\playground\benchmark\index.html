<!DOCTYPE html>
<html>
<head>
  <title>Benchmark | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../assets/css/styles.css" rel="stylesheet">
  <style>
    body, html {
      background-color: black;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #stats {
      position: absolute;
      z-index: 2;
      right: 0;
      top: 0;
      background-color: rgba(0, 0, 0, .5);
      font-size: 12px;
      text-transform: uppercase;
      padding: 1em;
    }
    #stats fieldset {
      display: flex;
      padding: 0;
      margin: 0;
      flex-direction: column;
      font-family: monospace;
      min-width: 13em;
      border: none;
    }
    #stats legend {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      border-top: 1px solid currentColor;
      border-bottom: 1px solid currentColor;
      padding: 0;
      margin: 0;
    }
    #stats label {
      display: flex;
      justify-content: space-between;
    }
    #stats input {
      font-family: inherit;
      border: none;
      padding: 0;
      margin: 0;
      background: transparent;
      color: currentColor;
      text-align: right;
      font-size: inherit;
    }
    #stats button,
    #stats select {
      font-family: inherit;
      border: none;
      padding: 0;
      background: #FFF;
      color: #000;
      height: 1em;
      font-size: inherit;
      line-height: 1em;
    }
    #stats button:not(:last-child),
    #stats select:not(:last-child),
    #stats fieldset:not(:last-child) {
      margin-bottom: 1ch;
    }
    #particles-container {
      pointer-events: none;
      display: flex;
      position: relative;
      width: 100%;
      height: 100vh;
      max-width: 1440px;
      overflow: hidden;
    }
    .particle {
      position: absolute;
      z-index: 1;
      left: 50%;
      top: 50%;
      width: .5rem;
      height: .5rem;
      margin: -.25rem 0 0 -.25rem;
      background: currentColor;
/*      border-radius: 50%;*/
    }
    .color1 { color: #FF4B4B; }
    .color2 { color: #9F3A39; }
    .color3 { color: #CF4242; }
  </style>
</head>
<body>
  <div id="particles-container"></div>
  <div id="stats">
    <fieldset>
      <legend>MONITOR</legend>
      <label>FPS<input type="text" id="fps" value="00.00" size=6 /></label>
      <label>AVG FPS<input type="text" id="avg" value="0" size=6 /></label>
    </fieldset>
    <fieldset>
      <legend>RENDER MODE</legend>
      <label>Anime.js<input class="config-item" type="radio" name="mode" value="css-transform"></label>
      <label>Anime.js + WAAPI<input class="config-item" type="radio" name="mode" value="anime-waapi-css-transform"></label>
      <label>WAAPI<input class="config-item" type="radio" name="mode" value="wappi-css-transform"></label>
      <label>Anime.js + Three.js<input class="config-item" type="radio" name="mode" value="webgl-threejs"></label>
    </fieldset>
    <fieldset>
      <legend>ANIMATIONS<input class="config-item" type="text" name="count" value="0" size=6 /></label></legend>
      <label for="count-range">COUNT</label>
      <input id="count-range" class="config-item" type="range" name="count" min="0" max="4000" value="0" step="500" />
    </fieldset>
    <fieldset>
      <legend>TWEENS<span id="active-tweens"></span></legend>
      <label>x<input class="config-item" type="checkbox" name="tween" value="x"></label>
      <label>y<input class="config-item" type="checkbox" name="tween" value="y"></label>
      <label>rotation<input class="config-item" type="checkbox" name="tween" value="rotation"></label>
      <label>scale<input class="config-item" type="checkbox" name="tween" value="scale"></label>
    </fieldset>
    <fieldset>
      <button id="restart-button" class="restart-button">RESTART</button>
    </fieldset>
  </div>
</body>
<script type="module" src="index.js"></script>
</html>
