<!DOCTYPE html>
<html>
<head>
  <title>Anime logo animation V2 / Anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      opacity: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    body.ready {
      opacity: 1;
    }
    .logo-animation {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
    }
    .letters {
      position: relative;
      display: flex;
      width: 682px;
      height: 162px;
    }
    .letter {
      position: relative;
      width: 162px;
      height: 162px;
    }
    .letter:not(:first-child) {
      margin-left: -42px;
    }
    .letter-i {
      z-index: 1;
      width: 82px;
      transform-origin: 100% 50%;
    }
    .dot {
      position: absolute;
      width: 42px;
      height: 42px;
      transform: scale(0);
    }
    .dot-i {
      top: 0;
      left: 240px;
    }
    .dot-e {
      z-index: 0;
      bottom: 0;
      right: 0;
    }
    .logo-icon {
      display: flex;
      position: absolute;
      left: 230px;
      top: -10px;
      width: 222px;
      height: 62px;
    }
    .icon {
      width: 62px;
      height: 62px;
      opacity: 0;
    }
    .icon-text {
      position: absolute;
      top: 60px;
      left: 60px;
      width: 160px;
      height: 62px;
    }
    .icon-text path,
    .icon-text polygon {
      opacity: 0;
    }
  </style>
</head>
<body>
  <div class="logo-animation">
    <div class="letters">
      <div class="letter letter-a">
        <svg viewBox="0 0 162 162">
          <g fill="none" fill-rule="evenodd" stroke="#5E89FB">
            <path class="fill in" stroke-width="40" d="M101 141H81a60 60 0 1 1 0-120c33.14 0 59 26.86 60 60v80"/>
            <path class="fill out" stroke-width="40" d="M141 161V81c-1-33.14-26.86-60-60-60a60 60 0 1 0 0 120h20"/>
            <path class="line out" stroke-width="2" d="M121 161V81.33C120.18 58.59 102.7 41 81 41a40 40 0 1 0 0 80h20v40H81A80 80 0 1 1 81 1c43.8 0 78.66 35.27 80 79.7V161h-40z"/>
          </g>
        </svg>
      </div>
      <div class="letter letter-n">
        <svg viewBox="0 0 162 162">
          <g fill="none" fill-rule="evenodd" stroke="#FB155A">
            <path class="fill in" stroke-width="40" d="M21 161V1"/>
            <path class="fill out" stroke-width="40" d="M21 1v160"/>
            <path class="fill in" stroke-width="40" d="M21 161V81c1-33.14 26.86-60 60-60a60 60 0 0 1 60 60v80"/>
            <path class="fill out" stroke-width="40" d="M141 161V81a60 60 0 0 0-60-60c-33.14 0-59 26.86-60 60v80"/>
            <path class="line out" stroke-width="2" d="M41 161V1H1v160h40z"/>
            <path class="line out" stroke-width="2" d="M1 161V80.4C2.35 36.27 37.2 1 81 1a80 80 0 0 1 80 80v80h-40V81a40 40 0 0 0-40-40c-21.7 0-39.18 17.59-40 40.33V161H1z"/>
          </g>
        </svg>
      </div>
      <div class="letter letter-i">
        <svg viewBox="0 0 82 162">
          <g fill="none" fill-rule="evenodd" stroke="#18FF92">
            <path class="fill in" stroke-width="40" d="M21 61v20a60 60 0 0 0 60 60"/>
            <path class="fill out" stroke-width="40" d="M81 141a60 60 0 0 1-60-60V61"/>
            <path class="line out" stroke-width="2" d="M81 121a40 40 0 0 1-40-40V61H1v20a80 80 0 0 0 80 80v-40z"/>
          </g>
        </svg>
      </div>
      <div class="letter letter-m-1">
        <svg viewBox="0 0 162 162">
          <g fill="none" fill-rule="evenodd" stroke="#5E89FB">
            <path class="fill in" stroke-width="40" d="M21 161V1"/>
            <path class="fill out" stroke-width="40" d="M21 1v160"/>
            <path class="fill in" stroke-width="40" d="M21 161V81c1-33.14 26.86-60 60-60a60 60 0 0 1 60 60v80"/>
            <path class="fill out" stroke-width="40" d="M141 161V81a60 60 0 0 0-60-60c-33.14 0-59 26.86-60 60v80"/>
            <path class="line out" stroke-width="2" d="M41 161V1H1v160h40z"/>
            <path class="line out" stroke-width="2" d="M1 161V80.4C2.35 36.27 37.2 1 81 1a80 80 0 0 1 80 80v80h-40V81a40 40 0 0 0-40-40c-21.7 0-39.18 17.59-40 40.33V161H1z"/>
          </g>
        </svg>
      </div>
      <div class="letter letter-m-2">
        <svg viewBox="0 0 162 162">
          <g fill="none" fill-rule="evenodd" stroke="#FB155A">
            <path class="fill in" stroke-width="40" d="M21 161V81c1-33.14 26.86-60 60-60a60 60 0 0 1 60 60v80"/>
            <path class="fill out" stroke-width="40" d="M141 161V81a60 60 0 0 0-60-60c-33.14 0-59 26.86-60 60v80"/>
            <path class="line out" stroke-width="2" d="M1 161V80.4C2.35 36.27 37.2 1 81 1a80 80 0 0 1 80 80v80h-40V81a40 40 0 0 0-40-40c-21.7 0-39.18 17.59-40 40.33V161H1z"/>
          </g>
        </svg>
      </div>
      <div class="letter letter-e">
        <svg viewBox="0 0 162 162">
          <g fill="none" fill-rule="evenodd" stroke="#18FF92">
            <path class="fill in" stroke-width="40" d="M81 101h60V81c-1-33.14-26.86-60-60-60a60 60 0 1 0 0 120"/>
            <path class="fill out" stroke-width="40" d="M81 141a60 60 0 1 1 0-120c33.14 0 59 26.86 60 60v20H81"/>
            <path class="line out" stroke-width="2" d="M81 81v40h80V80.7C159.66 36.27 124.8 1 81 1a80 80 0 1 0 0 160v-40a40 40 0 1 1 0-80c21.6 0 39.01 17.42 39.99 40H81z"/>
          </g>
        </svg>
      </div>
      <div class="logo-icon">
        <div class="icon">
          <svg viewBox="0 0 62 62">
            <g fill="none" fill-rule="evenodd" stroke-width="2" transform="translate(1 1)">
              <path class="icon-curve" stroke="#FF1554" d="M0 16a80.88 80.88 0 0 1 44 44"/>
              <path class="icon-line" stroke="#5E89FB" d="M4 0h54a2 2 0 0 1 2 2.01V58A2 2 0 0 1 58 60H2a2 2 0 0 1-2-2.01V2A2 2 0 0 1 2 0h2z"/>
              <rect width="40" height="40" x="10" y="10" stroke="#18FF92" rx="20"/>
            </g>
          </svg>
        </div>
        <div class="icon-text">
          <svg viewBox="0 0 160 62">
            <g fill="#FBF3FB" fill-rule="evenodd">
              <path d="M27.33 18h1.73l10.15 25.7h-1.69l-3.24-8.24H21.97l-3.28 8.24H17L27.33 18zm6.45 16.1l-5.51-14.55h-.07l-5.73 14.54h11.3z"/>
              <polygon points="51.334 18 53.314 18 69.55 41.58 69.622 41.58 69.622 18 71.206 18 71.206 43.704 69.334 43.704 52.99 19.944 52.918 19.944 52.918 43.704 51.334 43.704"/>
              <polygon points="86.027 18 87.611 18 87.611 43.704 86.027 43.704"/>
              <polygon points="102.433 18 104.701 18 114.745 41.94 114.817 41.94 124.753 18 127.021 18 127.021 43.704 125.437 43.704 125.437 19.944 125.365 19.944 115.573 43.704 113.989 43.704 104.089 19.944 104.017 19.944 104.017 43.704 102.433 43.704"/>
              <polygon points="141.843 18 159.123 18 159.123 19.368 143.427 19.368 143.427 29.664 158.187 29.664 158.187 31.032 143.427 31.032 143.427 42.336 159.303 42.336 159.303 43.704 141.843 43.704"/>
            </g>
          </svg>
        </div>
      </div>
      <div class="dot dot-i">
        <svg viewBox="0 0 42 42">
          <g fill="none" fill-rule="evenodd">
            <rect width="40" height="40" x="1" y="1" fill="#17F28C" rx="20"/>
          </g>
        </svg>
      </div>
      <div class="dot dot-e">
        <svg viewBox="0 0 42 42">
          <g fill="none" fill-rule="evenodd">
            <rect width="40" height="40" x="1" y="1" fill="#FFFFFF" rx="20"/>
          </g>
        </svg>
      </div>
    </div>
  </div>
  <script type="module">

    import { createTimeline, utils, stagger, svg } from '../../lib/anime.esm.js';
    import { inspect } from '../../lib/gui/index.js';

    utils.set(['.fill.out', '.line.out'], { opacity: 0 });

    const logoTimeline = createTimeline({
      autoplay: false,
      alternate: true,
      loop: 1,
      defaults: {
        ease: 'outElastic(1, .5)',
      }
    })
    .add('.dot-e', {
      translateX: [
        { to: -600, duration: 520, delay: 200, ease: 'inQuart' },
        { to: [-100, 0], duration: 500, delay: 1000, ease: 'outQuart' }
      ],
      scale: [
        { to: [0, 1], duration: 200, ease: 'outBack' },
        { to: [0, 0], duration: 100, delay: 500, ease: 'inQuart' },
        { to: 1, duration: 200, delay: 1000, ease: 'outQuart' },
        { to: 0, duration: 400, delay: 500, ease: 'inBack' }
      ]
    }, 0)
    .add('.dot-i', {
      translateY: { to: [-200, 0], duration: 500, ease: 'outElastic(1, .8)', },
      scale: [
        { to: [0, 1], duration: 100, ease: 'outQuart' },
        { to: 0, duration: 400, delay: 1400, ease: 'inBack' }
      ],
      delay: 1200
    }, 0)
    .add(svg.createDrawable('.fill.in'), {
      strokeDashoffset: {
        draw: '0 1',
        duration: 600,
        ease: 'outQuart',
      },
      stroke: {
        to: ['#FFF', el => utils.get(el.parentNode, 'stroke')],
        duration: 350,
        ease: 'inQuad',
      },
      delay: stagger(100, { start: 700 }),
    }, 0)
    .add(svg.createDrawable('.fill.out'), {
      strokeDashoffset: [
        {
          to: '1 1',
          duration: 800,
          delay: stagger(80),
          ease: 'inQuart'
        }
      ]
    }, 1890)
    .add(svg.createDrawable('.line.out'), {
      opacity: {
        to: 1,
        duration: 100,
      },
      strokeDashoffset: {
        draw: ['0 1', '1 1'],
        duration: 1200,
        delay: stagger(100, { start: 500 }),
        ease: 'inQuart'
      },
      strokeWidth: {
        to: [0, 2],
        delay: stagger(100),
        duration: 200,
        ease: 'linear'
      }
    }, 2000)
    .add('.icon', {
      opacity: { to: 1, duration: 10, delay: 2800, ease: 'linear' },
      translateY: { to: 60, duration: 800 },
      ease: 'outElastic(1, .5)',
      delay: 4200
    }, 0)
    .add(svg.createDrawable('.icon-line'), {
      strokeDashoffset: {
        draw: '0 1',
        duration: 1200,
        ease: 'inOutQuart',
      },
      strokeWidth: {
        to: [8, 2],
        duration: 800,
        ease: 'inQuad'
      },
      stroke: {
        from: '#FFF',
        duration: 800,
        delay: 400,
        ease: 'inQuad'
      }
    }, 3000)
    .add(['.icon-text path', '.icon-text polygon'], {
      translateY: { to: [50, 0] },
      opacity: { to: 1, duration: 100, ease: 'linear' },
      ease: 'outElastic(1, .5)',
      delay: stagger(20),
    }, 4200)
    .init();

    inspect(logoTimeline);

    // logoTimeline.seek(660);
    // logoTimeline.seek(660);

    document.body.classList.add('ready');
    // logoTimeline.play();

  </script>
</body>
</html>
