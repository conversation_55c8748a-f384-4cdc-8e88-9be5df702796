section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 100lvh;
  border-top: 1px dotted var(--red);
  border-bottom: 1px dotted var(--red);
}

section.spacer {
  min-height: 50lvh;
}

h2 {
  font-family: ui-monospace, monospace;
  font-size: min(5vw, 24px);
  max-width: 1000px;
}

.section-content {
  transform-style: preserve-3d;
  display: flex;
  width: 100%;
  height: 100lvh;
  justify-content: center;
}

.stack {
  --w: 22.5vh;
  --h: 30vh;
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
  transform-style: preserve-3d;
  width: calc(var(--w) * 1);
  height: calc(var(--h) * 1);
  will-change: transform;
}

.card {
  transform-style: preserve-3d;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 1rem;
  transform-origin: 50% 105% 0px;
  box-shadow: 0 .125em .75em 1px rgba(0,0,0,.05),
              0 0 .125em 1px rgba(0,0,0,.1);
}

.card > div {
  transform-style: preserve-3d;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  border-radius: inherit;
}

.back {
  z-index: 0;
  background-image: url(card-back.svg);
  transform: rotateY(180deg);
  background-color: var(--white);
}

.front {
  z-index: 1;
  backface-visibility: hidden;
  background-image: url(card.svg);
  background-color: var(--white);
  background-size: 89%;
  background-repeat: no-repeat;
}
