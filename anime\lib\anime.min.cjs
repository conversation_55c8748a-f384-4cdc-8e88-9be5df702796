/**
 * anime.js - CJS
 * @version v4.1.3
 * <AUTHOR>
 * @license MIT
 * @copyright (c) 2025 <PERSON>
 * @see https://animejs.com
 */
"use strict";const t="undefined"!=typeof window,e=t?window:null,s=t?document:null,i={replace:0,none:1,blend:2},r=Symbol(),n=Symbol(),o=Symbol(),a=Symbol(),h=Symbol(),l=Symbol(),c=1e-11,d=1e12,u=1e3,p="",m=(()=>{const t=new Map;return t.set("x","translateX"),t.set("y","translateY"),t.set("z","translateZ"),t})(),f=["translateX","translateY","translateZ","rotate","rotateX","rotateY","rotateZ","scale","scaleX","scaleY","scaleZ","skew","skewX","skewY","perspective","matrix","matrix3d"],g=f.reduce(((t,e)=>({...t,[e]:e+"("})),{}),y=()=>{},_=/(^#([\da-f]{3}){1,2}$)|(^#([\da-f]{4}){1,2}$)/i,v=/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i,b=/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(-?\d+|-?\d*.\d+)\s*\)/i,x=/hsl\(\s*(-?\d+|-?\d*.\d+)\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)%\s*\)/i,T=/hsla\(\s*(-?\d+|-?\d*.\d+)\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)\s*\)/i,w=/[-+]?\d*\.?\d+(?:e[-+]?\d)?/gi,S=/^([-+]?\d*\.?\d+(?:e[-+]?\d+)?)([a-z]+|%)$/i,k=/([a-z])([A-Z])/g,$=/(\w+)(\([^)]+\)+)/g,C=/(\*=|\+=|-=)/,E={id:null,keyframes:null,playbackEase:null,playbackRate:1,frameRate:120,loop:0,reversed:!1,alternate:!1,autoplay:!0,duration:u,delay:0,loopDelay:0,ease:"out(2)",composition:i.replace,modifier:t=>t,onBegin:y,onBeforeUpdate:y,onUpdate:y,onLoop:y,onPause:y,onComplete:y,onRender:y},B={current:null,root:s},L={defaults:E,precision:4,timeScale:1,tickThreshold:200},D={version:"4.1.3",engine:null};t&&(e.AnimeJS||(e.AnimeJS=[]),e.AnimeJS.push(D));const A=t=>t.replace(k,"$1-$2").toLowerCase(),N=(t,e)=>0===t.indexOf(e),F=Date.now,O=Array.isArray,P=t=>t&&t.constructor===Object,R=t=>"number"==typeof t&&!isNaN(t),M=t=>"string"==typeof t,I=t=>"function"==typeof t,z=t=>void 0===t,Y=t=>z(t)||null===t,X=e=>t&&e instanceof SVGElement,W=t=>_.test(t),V=t=>N(t,"rgb"),H=t=>N(t,"hsl"),U=t=>!L.defaults.hasOwnProperty(t),q=t=>M(t)?parseFloat(t):t,Q=Math.pow,j=Math.sqrt,G=Math.sin,Z=Math.cos,J=Math.abs,K=Math.exp,tt=Math.ceil,et=Math.floor,st=Math.asin,it=Math.max,rt=Math.atan2,nt=Math.PI,ot=Math.round,at=(t,e,s)=>t<e?e:t>s?s:t,ht={},lt=(t,e)=>{if(e<0)return t;if(!e)return ot(t);let s=ht[e];return s||(s=ht[e]=10**e),ot(t*s)/s},ct=(t,e)=>O(e)?e.reduce(((e,s)=>J(s-t)<J(e-t)?s:e)):e?ot(t/e)*e:t,dt=(t,e,s)=>t+(e-t)*s,ut=(t,e,s)=>{const i=10**(s||0);return et((Math.random()*(e-t+1/i)+t)*i)/i},pt=t=>{let e,s,i=t.length;for(;i;)s=ut(0,--i),e=t[i],t[i]=t[s],t[s]=e;return t},mt=t=>t===1/0?d:t===-1/0?-1e12:t,ft=t=>t<=c?c:mt(lt(t,11)),gt=t=>O(t)?[...t]:t,yt=(t,e)=>{const s={...t};for(let i in e){const r=t[i];s[i]=z(r)?e[i]:r}return s},_t=(t,e,s,i="_prev",r="_next")=>{let n=t._head,o=r;for(s&&(n=t._tail,o=i);n;){const t=n[o];e(n),n=t}},vt=(t,e,s="_prev",i="_next")=>{const r=e[s],n=e[i];r?r[i]=n:t._head=n,n?n[s]=r:t._tail=r,e[s]=null,e[i]=null},bt=(t,e,s,i="_prev",r="_next")=>{let n=t._tail;for(;n&&s&&s(n,e);)n=n[i];const o=n?n[r]:t._head;n?n[r]=e:t._head=e,o?o[i]=e:t._tail=e,e[i]=n,e[r]=o},xt=t=>{let e;return(...s)=>{let i,r,n,o;e&&(i=e.currentIteration,r=e.iterationProgress,n=e.reversed,o=e._alternate,e.revert());const a=t(...s);return a&&!I(a)&&a.revert&&(e=a),z(r)||(e.currentIteration=i,e.iterationProgress=(o&&i%2?!n:n)?1-r:r),a||y}};class Tt{constructor(t=0){this.deltaTime=0,this._currentTime=t,this._elapsedTime=t,this._startTime=t,this._lastTime=t,this._scheduledTime=0,this._frameDuration=lt(u/120,0),this._fps=120,this._speed=1,this._hasChildren=!1,this._head=null,this._tail=null}get fps(){return this._fps}set fps(t){const e=this._frameDuration,s=+t,i=s<c?c:s,r=lt(u/i,0);this._fps=i,this._frameDuration=r,this._scheduledTime+=r-e}get speed(){return this._speed}set speed(t){const e=+t;this._speed=e<c?c:e}requestTick(t){const e=this._scheduledTime,s=this._elapsedTime;if(this._elapsedTime+=t-s,s<e)return 0;const i=this._frameDuration,r=s-e;return this._scheduledTime+=r<i?i:r,1}computeDeltaTime(t){const e=t-this._lastTime;return this.deltaTime=e,this._lastTime=t,e}}const wt=(t,e,s,r,n)=>{const o=t.parent,h=t.duration,l=t.completed,d=t.iterationDuration,u=t.iterationCount,m=t._currentIteration,f=t._loopDelay,y=t._reversed,_=t._alternate,v=t._hasChildren,b=t._delay,x=t._currentTime,T=b+d,w=e-b,S=at(x,-b,h),k=at(w,-b,h),$=w-x,C=k>0,E=k>=h,B=h<=c,D=2===n;let A=0,N=w,F=0;if(u>1){const e=~~(k/(d+(E?0:f)));t._currentIteration=at(e,0,u),E&&t._currentIteration--,A=t._currentIteration%2,N=k%(d+f)||0}const O=y^(_&&A),P=t._ease;let R=E?O?0:h:O?d-N:N;P&&(R=d*P(R/d)||0);const M=(o?o.backwards:w<x)?!O:!!O;if(t._currentTime=w,t._iterationTime=R,t.backwards=M,C&&!t.began?(t.began=!0,s||o&&(M||!o.began)||t.onBegin(t)):w<=0&&(t.began=!1),s||v||!C||t._currentIteration===m||t.onLoop(t),D||1===n&&(e>=b&&e<=T||e<=b&&S>b||e>=T&&S!==h)||R>=T&&S!==h||R<=b&&S>0||e<=S&&S===h&&l||E&&!l&&B){if(C&&(t.computeDeltaTime(S),s||t.onBeforeUpdate(t)),!v){const e=D||(M?-1*$:$)>=L.tickThreshold,n=t._offset+(o?o._offset:0)+b+R;let h,l,c,d,u=t._head,m=0;for(;u;){const t=u._composition,s=u._currentTime,o=u._changeDuration,f=u._absoluteStartTime+u._changeDuration,y=u._nextRep,_=u._prevRep,v=t!==i.none;if((e||(s!==o||n<=f+(y?y._delay:0))&&(0!==s||n>=u._absoluteStartTime))&&(!v||!u._isOverridden&&(!u._isOverlapped||n<=f)&&(!y||y._isOverridden||n<=y._absoluteStartTime)&&(!_||_._isOverridden||n>=_._absoluteStartTime+_._changeDuration+u._delay))){const e=u._currentTime=at(R-u._startTime,0,o),s=u._ease(e/u._updateDuration),n=u._modifier,p=u._valueType,f=u._tweenType,g=0===f,y=0===p,_=y&&g||0===s||1===s?-1:L.precision;let b,x;if(y)b=x=n(lt(dt(u._fromNumber,u._toNumber,s),_));else if(1===p)x=n(lt(dt(u._fromNumber,u._toNumber,s),_)),b=`${x}${u._unit}`;else if(2===p){const t=u._fromNumbers,e=u._toNumbers,i=lt(at(n(dt(t[0],e[0],s)),0,255),0),r=lt(at(n(dt(t[1],e[1],s)),0,255),0),o=lt(at(n(dt(t[2],e[2],s)),0,255),0),a=at(n(lt(dt(t[3],e[3],s),_)),0,1);if(b=`rgba(${i},${r},${o},${a})`,v){const t=u._numbers;t[0]=i,t[1]=r,t[2]=o,t[3]=a}}else if(3===p){b=u._strings[0];for(let t=0,e=u._toNumbers.length;t<e;t++){const e=n(lt(dt(u._fromNumbers[t],u._toNumbers[t],s),_)),i=u._strings[t+1];b+=`${i?e+i:e}`,v&&(u._numbers[t]=e)}}if(v&&(u._number=x),r||t===i.blend)u._value=b;else{const t=u.property;h=u.target,g?h[t]=b:1===f?h.setAttribute(t,b):(l=h.style,3===f?(h!==c&&(c=h,d=h[a]),d[t]=b,m=1):2===f?l[t]=b:4===f&&l.setProperty(t,b)),C&&(F=1)}}if(m&&u._renderTransforms){let t=p;for(let e in d)t+=`${g[e]}${d[e]}) `;l.transform=t,m=0}u=u._next}!s&&F&&t.onRender(t)}!s&&C&&t.onUpdate(t)}return o&&B?!s&&(o.began&&!M&&w>=h&&!l||M&&w<=c&&l)&&(t.onComplete(t),t.completed=!M):C&&E?u===1/0?t._startTime+=t.duration:t._currentIteration>=u-1&&(t.paused=!0,l||v||(t.completed=!0,s||o&&(M||!o.began)||(t.onComplete(t),t._resolve(t)))):t.completed=!1,F},St=(t,e,s,i,r)=>{const n=t._currentIteration;if(wt(t,e,s,i,r),t._hasChildren){const o=t,a=o.backwards,h=i?e:o._iterationTime,l=F();let d=0,u=!0;if(!i&&o._currentIteration!==n){const t=o.iterationDuration;_t(o,(e=>{if(a){const i=e.duration,r=e._offset+e._delay;s||!(i<=c)||r&&r+i!==t||e.onComplete(e)}else!e.completed&&!e.backwards&&e._currentTime<e.iterationDuration&&wt(e,t,s,1,2),e.began=!1,e.completed=!1})),s||o.onLoop(o)}_t(o,(t=>{const e=lt((h-t._offset)*t._speed,12),n=t._fps<o._fps?t.requestTick(l):r;d+=wt(t,e,s,i,n),!t.completed&&u&&(u=!1)}),a),!s&&d&&o.onRender(o),(u||a)&&o._currentTime>=o.duration&&(o.paused=!0,o.completed||(o.completed=!0,s||(o.onComplete(o),o._resolve(o))))}},kt={animation:null,update:y},$t=(()=>t?requestAnimationFrame:setImmediate)(),Ct=(()=>t?cancelAnimationFrame:clearImmediate)();class Et extends Tt{constructor(t){super(t),this.useDefaultMainLoop=!0,this.pauseOnDocumentHidden=!0,this.defaults=E,this.paused=!0,this.reqId=0}update(){const t=this._currentTime=F();if(this.requestTick(t)){this.computeDeltaTime(t);const e=this._speed,s=this._fps;let i=this._head;for(;i;){const r=i._next;i.paused?(vt(this,i),this._hasChildren=!!this._tail,i._running=!1,i.completed&&!i._cancelled&&i.cancel()):St(i,(t-i._startTime)*i._speed*e,0,0,i._fps<s?i.requestTick(t):1),i=r}kt.update()}}wake(){return this.useDefaultMainLoop&&!this.reqId&&(this.requestTick(F()),this.reqId=$t(Lt)),this}pause(){if(this.reqId)return this.paused=!0,Dt()}resume(){if(this.paused)return this.paused=!1,_t(this,(t=>t.resetTime())),this.wake()}get speed(){return this._speed*(1===L.timeScale?1:u)}set speed(t){this._speed=t*L.timeScale,_t(this,(t=>t.speed=t._speed))}get timeUnit(){return 1===L.timeScale?"ms":"s"}set timeUnit(t){const e="s"===t,s=e?.001:1;if(L.timeScale!==s){L.timeScale=s,L.tickThreshold=200*s;const t=e?.001:u;this.defaults.duration*=t,this._speed*=t}}get precision(){return L.precision}set precision(t){L.precision=t}}const Bt=(()=>{const e=new Et(F());return t&&(D.engine=e,s.addEventListener("visibilitychange",(()=>{e.pauseOnDocumentHidden&&(s.hidden?e.pause():e.resume())}))),e})(),Lt=()=>{Bt._head?(Bt.reqId=$t(Lt),Bt.update()):Bt.reqId=0},Dt=()=>(Ct(Bt.reqId),Bt.reqId=0,Bt);function At(t){const e=M(t)?B.root.querySelectorAll(t):t;if(e instanceof NodeList||e instanceof HTMLCollection)return e}function Nt(e){if(Y(e))return[];if(!t)return O(e)&&e.flat(1/0)||[e];if(O(e)){const t=e.flat(1/0),s=[];for(let e=0,i=t.length;e<i;e++){const i=t[e];if(!Y(i)){const t=At(i);if(t)for(let e=0,i=t.length;e<i;e++){const i=t[e];if(!Y(i)){let t=!1;for(let e=0,r=s.length;e<r;e++)if(s[e]===i){t=!0;break}t||s.push(i)}}else{let t=!1;for(let e=0,r=s.length;e<r;e++)if(s[e]===i){t=!0;break}t||s.push(i)}}}return s}const s=At(e);return s?Array.from(s):[e]}function Ft(t){const e=Nt(t),s=e.length;if(s)for(let t=0;t<s;t++){const s=e[t];if(!s[r]){s[r]=!0;const t=X(s);(s.nodeType||t)&&(s[n]=!0,s[o]=t,s[a]={})}}return e}const Ot=t=>{const e=Nt(t)[0];if(e&&X(e))return e},Pt=(t,e,s=0)=>t.getPointAtLength(e+s>=1?e+s:0),Rt=(t,e)=>s=>{const i=+t.getTotalLength(),r=s[o],n=t.getCTM();return{from:0,to:i,modifier:s=>{if("a"===e){const e=Pt(t,s,-1),i=Pt(t,s,1);return 180*rt(i.y-e.y,i.x-e.x)/nt}{const i=Pt(t,s,0);return"x"===e?r||!n?i.x:i.x*n.a+i.y*n.c+n.e:r||!n?i.y:i.x*n.b+i.y*n.d+n.f}}}},Mt=["opacity","rotate","overflow","color"],It={morphTo:(t,e=.33)=>s=>{const i=Ot(t);if(!i)return;const r="path"===s.tagName,n=r?" ":",",o=s[h];o&&s.setAttribute(r?"d":"points",o);let a="",l="";if(e){const t=s.getTotalLength(),o=i.getTotalLength(),h=Math.max(Math.ceil(t*e),Math.ceil(o*e));for(let e=0;e<h;e++){const c=e/(h-1),d=s.getPointAtLength(t*c),u=i.getPointAtLength(o*c),p=r?0===e?"M":"L":"";a+=p+lt(d.x,3)+n+d.y+" ",l+=p+lt(u.x,3)+n+u.y+" "}}else a=s.getAttribute(r?"d":"points"),l=i.getAttribute(r?"d":"points");return s[h]=l,[a,l]},createMotionPath:t=>{const e=Ot(t);if(e)return{translateX:Rt(e,"x"),translateY:Rt(e,"y"),rotate:Rt(e,"a")}},createDrawable:(t,e=0,s=0)=>Nt(t).map((t=>((t,e,s)=>{const i=u,r=getComputedStyle(t),n=r.strokeLinecap,o="non-scaling-stroke"===r.vectorEffect?t:null;let a=n;const h=new Proxy(t,{get(t,e){const s=t[e];return e===l?t:"setAttribute"===e?(...e)=>{if("draw"===e[0]){const s=e[1].split(" "),r=+s[0],h=+s[1],l=(t=>{let e=1;if(t&&t.getCTM){const s=t.getCTM();s&&(e=(j(s.a*s.a+s.b*s.b)+j(s.c*s.c+s.d*s.d))/2)}return e})(o),c=-1e3*r*l,d=h*i*l+c,u=i*l+(0===r&&1===h||1===r&&0===h?0:10*l)-d;if("butt"!==n){const e=r===h?"butt":n;a!==e&&(t.style.strokeLinecap=`${e}`,a=e)}t.setAttribute("stroke-dashoffset",`${c}`),t.setAttribute("stroke-dasharray",`${d} ${u}`)}return Reflect.apply(s,t,e)}:I(s)?(...e)=>Reflect.apply(s,t,e):s}});return"1000"!==t.getAttribute("pathLength")&&(t.setAttribute("pathLength","1000"),h.setAttribute("draw",`${e} ${s}`)),h})(t,e,s)))},zt=(t,e,s)=>(s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+6*(e-t)*s:s<.5?e:s<2/3?t+(e-t)*(2/3-s)*6:t),Yt=(t,e)=>z(t)?e:t,Xt=(t,e,s,i,r)=>{if(I(t)){const n=()=>{const r=t(e,s,i);return isNaN(+r)?r||0:+r};return r&&(r.func=n),n()}return t},Wt=(t,e)=>t[n]?t[o]&&((t,e)=>{if(Mt.includes(e))return!1;if(t.getAttribute(e)||e in t){if("scale"===e){const e=t.parentNode;return e&&"filter"===e.tagName}return!0}})(t,e)?1:f.includes(e)||m.get(e)?3:N(e,"--")?4:e in t.style?2:e in t?0:1:0,Vt=(t,e,s)=>{const i=t.style[e];i&&s&&(s[e]=i);const r=i||getComputedStyle(t[l]||t).getPropertyValue(e);return"auto"===r?"0":r},Ht=(t,e,s,i)=>{const r=z(s)?Wt(t,e):s;return 0===r?t[e]||0:1===r?t.getAttribute(e):3===r?((t,e,s)=>{const i=t.style.transform;let r;if(i){const n=t[a];let o;for(;o=$.exec(i);){const t=o[1],i=o[2].slice(1,-1);n[t]=i,t===e&&(r=i,s&&(s[e]=i))}}return i&&!z(r)?r:N(e,"scale")?"1":N(e,"rotate")||N(e,"skew")?"0deg":"0px"})(t,e,i):4===r?Vt(t,e,i).trimStart():Vt(t,e,i)},Ut=(t,e,s)=>"-"===s?t-e:"+"===s?t+e:t*e,qt=(t,e)=>{if(e.t=0,e.n=0,e.u=null,e.o=null,e.d=null,e.s=null,!t)return e;const s=+t;if(isNaN(s)){let s=t;"="===s[1]&&(e.o=s[0],s=s.slice(2));const n=!s.includes(" ")&&S.exec(s);if(n)return e.t=1,e.n=+n[1],e.u=n[2],e;if(e.o)return e.n=+s,e;if(W(r=s)||V(r)||H(r))return e.t=2,e.d=V(i=s)?(t=>{const e=v.exec(t)||b.exec(t),s=z(e[4])?1:+e[4];return[+e[1],+e[2],+e[3],s]})(i):W(i)?(t=>{const e=t.length,s=4===e||5===e;return[+("0x"+t[1]+t[s?1:2]),+("0x"+t[s?2:3]+t[s?2:4]),+("0x"+t[s?3:5]+t[s?3:6]),5===e||9===e?+(+("0x"+t[s?4:7]+t[s?4:8])/255).toFixed(3):1]})(i):H(i)?(t=>{const e=x.exec(t)||T.exec(t),s=+e[1]/360,i=+e[2]/100,r=+e[3]/100,n=z(e[4])?1:+e[4];let o,a,h;if(0===i)o=a=h=r;else{const t=r<.5?r*(1+i):r+i-r*i,e=2*r-t;o=lt(255*zt(e,t,s+1/3),0),a=lt(255*zt(e,t,s),0),h=lt(255*zt(e,t,s-1/3),0)}return[o,a,h,n]})(i):[0,0,0,1],e;{const t=s.match(w);return e.t=3,e.d=t?t.map(Number):[],e.s=s.split(w)||[],e}}var i,r;return e.n=s,e},Qt=(t,e)=>(e.t=t._valueType,e.n=t._toNumber,e.u=t._unit,e.o=null,e.d=gt(t._toNumbers),e.s=gt(t._strings),e),jt={t:0,n:0,u:null,o:null,d:null,s:null},Gt={_rep:new WeakMap,_add:new Map},Zt=(t,e,s="_rep")=>{const i=Gt[s];let r=i.get(t);return r||(r={},i.set(t,r)),r[e]?r[e]:r[e]={_head:null,_tail:null}},Jt=(t,e)=>t._isOverridden||t._absoluteStartTime>e._absoluteStartTime,Kt=t=>{t._isOverlapped=1,t._isOverridden=1,t._changeDuration=c,t._currentTime=c},te=(t,e)=>{const s=t._composition;if(s===i.replace){const s=t._absoluteStartTime;bt(e,t,Jt,"_prevRep","_nextRep");const i=t._prevRep;if(i){const e=i.parent,r=i._absoluteStartTime+i._changeDuration;if(t.parent.id!==e.id&&e.iterationCount>1&&r+(e.duration-e.iterationDuration)>s){Kt(i);let t=i._prevRep;for(;t&&t.parent.id===e.id;)Kt(t),t=t._prevRep}const n=s-t._delay;if(r>n){const t=i._startTime,e=r-(t+i._updateDuration),s=lt(n-e-t,12);i._changeDuration=s,i._currentTime=s,i._isOverlapped=1,s<c&&Kt(i)}let o=!0;if(_t(e,(t=>{t._isOverlapped||(o=!1)})),o){const t=e.parent;if(t){let s=!0;_t(t,(t=>{t!==e&&_t(t,(t=>{t._isOverlapped||(s=!1)}))})),s&&t.cancel()}else e.cancel()}}}else if(s===i.blend){const e=Zt(t.target,t.property,"_add"),s=(t=>{let e=kt.animation;return e||(e={duration:c,computeDeltaTime:y,_offset:0,_delay:0,_head:null,_tail:null},kt.animation=e,kt.update=()=>{t.forEach((t=>{for(let e in t){const s=t[e],i=s._head;if(i){const t=i._valueType,e=3===t||2===t?gt(i._fromNumbers):null;let r=i._fromNumber,n=s._tail;for(;n&&n!==i;){if(e)for(let t=0,s=n._numbers.length;t<s;t++)e[t]+=n._numbers[t];else r+=n._number;n=n._prevAdd}i._toNumber=r,i._toNumbers=e}}})),wt(e,1,1,0,2)}),e})(Gt._add);let r=e._head;r||(r={...t},r._composition=i.replace,r._updateDuration=c,r._startTime=0,r._numbers=gt(t._fromNumbers),r._number=0,r._next=null,r._prev=null,bt(e,r),bt(s,r));const n=t._toNumber;if(t._fromNumber=r._fromNumber-n,t._toNumber=0,t._numbers=gt(t._fromNumbers),t._number=0,r._fromNumber=n,t._toNumbers){const e=gt(t._toNumbers);e&&e.forEach(((e,s)=>{t._fromNumbers[s]=r._fromNumbers[s]-e,t._toNumbers[s]=0})),r._fromNumbers=e}bt(e,t,null,"_prevAdd","_nextAdd")}return t},ee=t=>{const e=t._composition;if(e!==i.none){const s=t.target,r=t.property,n=Gt._rep.get(s)[r];if(vt(n,t,"_prevRep","_nextRep"),e===i.blend){const e=Gt._add,i=e.get(s);if(!i)return;const n=i[r],o=kt.animation;vt(n,t,"_prevAdd","_nextAdd");const a=n._head;if(a&&a===n._tail){vt(n,a,"_prevAdd","_nextAdd"),vt(o,a);let t=!0;for(let e in i)if(i[e]._head){t=!1;break}t&&e.delete(s)}}}return t},se=t=>(t.paused=!0,t.began=!1,t.completed=!1,t),ie=t=>t._cancelled?(t._hasChildren?_t(t,ie):_t(t,(t=>{t._composition!==i.none&&te(t,Zt(t.target,t.property))})),t._cancelled=0,t):t;let re=0;class ne extends Tt{constructor(t={},e=null,s=0){super(0);const{id:i,delay:r,duration:n,reversed:o,alternate:a,loop:h,loopDelay:l,autoplay:d,frameRate:u,playbackRate:p,onComplete:m,onLoop:f,onPause:g,onBegin:_,onBeforeUpdate:v,onUpdate:b}=t;B.current&&B.current.register(this);const x=e?0:Bt._elapsedTime,T=e?e.defaults:L.defaults,w=I(r)||z(r)?T.delay:+r,S=I(n)||z(n)?1/0:+n,k=Yt(h,T.loop),$=Yt(l,T.loopDelay),C=!0===k||k===1/0||k<0?1/0:k+1;let E=0;e?E=s:(Bt.reqId||Bt.requestTick(F()),E=(Bt._elapsedTime-Bt._startTime)*L.timeScale),this.id=z(i)?++re:i,this.parent=e,this.duration=mt((S+$)*C-$)||c,this.backwards=!1,this.paused=!0,this.began=!1,this.completed=!1,this.onBegin=_||T.onBegin,this.onBeforeUpdate=v||T.onBeforeUpdate,this.onUpdate=b||T.onUpdate,this.onLoop=f||T.onLoop,this.onPause=g||T.onPause,this.onComplete=m||T.onComplete,this.iterationDuration=S,this.iterationCount=C,this._autoplay=!e&&Yt(d,T.autoplay),this._offset=E,this._delay=w,this._loopDelay=$,this._iterationTime=0,this._currentIteration=0,this._resolve=y,this._running=!1,this._reversed=+Yt(o,T.reversed),this._reverse=this._reversed,this._cancelled=0,this._alternate=Yt(a,T.alternate),this._prev=null,this._next=null,this._elapsedTime=x,this._startTime=x,this._lastTime=x,this._fps=Yt(u,T.frameRate),this._speed=Yt(p,T.playbackRate)}get cancelled(){return!!this._cancelled}set cancelled(t){t?this.cancel():this.reset(1).play()}get currentTime(){return at(lt(this._currentTime,L.precision),-this._delay,this.duration)}set currentTime(t){const e=this.paused;this.pause().seek(+t),e||this.resume()}get iterationCurrentTime(){return lt(this._iterationTime,L.precision)}set iterationCurrentTime(t){this.currentTime=this.iterationDuration*this._currentIteration+t}get progress(){return at(lt(this._currentTime/this.duration,10),0,1)}set progress(t){this.currentTime=this.duration*t}get iterationProgress(){return at(lt(this._iterationTime/this.iterationDuration,10),0,1)}set iterationProgress(t){const e=this.iterationDuration;this.currentTime=e*this._currentIteration+e*t}get currentIteration(){return this._currentIteration}set currentIteration(t){this.currentTime=this.iterationDuration*at(+t,0,this.iterationCount-1)}get reversed(){return!!this._reversed}set reversed(t){t?this.reverse():this.play()}get speed(){return super.speed}set speed(t){super.speed=t,this.resetTime()}reset(t=0){return ie(this),this._reversed&&!this._reverse&&(this.reversed=!1),this._iterationTime=this.iterationDuration,St(this,0,1,t,2),se(this),this._hasChildren&&_t(this,se),this}init(t=0){this.fps=this._fps,this.speed=this._speed,!t&&this._hasChildren&&St(this,this.duration,1,t,2),this.reset(t);const e=this._autoplay;return!0===e?this.resume():e&&!z(e.linked)&&e.link(this),this}resetTime(){const t=1/(this._speed*Bt._speed);return this._startTime=F()-(this._currentTime+this._delay)*t,this}pause(){return this.paused||(this.paused=!0,this.onPause(this)),this}resume(){return this.paused?(this.paused=!1,this.duration<=c&&!this._hasChildren?St(this,c,0,0,2):(this._running||(bt(Bt,this),Bt._hasChildren=!0,this._running=!0),this.resetTime(),this._startTime-=12,Bt.wake()),this):this}restart(){return this.reset(0).resume()}seek(t,e=0,s=0){ie(this),this.completed=!1;const i=this.paused;return this.paused=!0,St(this,t+this._delay,~~e,~~s,1),i?this:this.resume()}alternate(){const t=this._reversed,e=this.iterationCount,s=this.iterationDuration,i=e===1/0?et(d/s):e;return this._reversed=+(!this._alternate||i%2?!t:t),e===1/0?this.iterationProgress=this._reversed?1-this.iterationProgress:this.iterationProgress:this.seek(s*i-this._currentTime),this.resetTime(),this}play(){return this._reversed&&this.alternate(),this.resume()}reverse(){return this._reversed||this.alternate(),this.resume()}cancel(){return this._hasChildren?_t(this,(t=>t.cancel()),!0):_t(this,ee),this._cancelled=1,this.pause()}stretch(t){const e=this.duration,s=ft(t);if(e===s)return this;const i=t/e,r=t<=c;return this.duration=r?c:s,this.iterationDuration=r?c:ft(this.iterationDuration*i),this._offset*=i,this._delay*=i,this._loopDelay*=i,this}revert(){St(this,0,1,0,1);const t=this._autoplay;return t&&t.linked&&t.linked===this&&t.revert(),this.cancel()}complete(){return this.seek(this.duration).cancel()}then(t=y){const e=this.then,s=()=>{this.then=null,t(this),this.then=e,this._resolve=y};return new Promise((t=>(this._resolve=()=>t(s()),this.completed&&this._resolve(),this)))}}const oe=t=>t,ae=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t,he=(t=.5,e=0,s=.5,i=1)=>t===e&&s===i?oe:r=>0===r||1===r?r:ae(((t,e,s)=>{let i,r,n=0,o=1,a=0;do{r=n+(o-n)/2,i=ae(r,e,s)-t,i>0?o=r:n=r}while(J(i)>1e-7&&++a<100);return r})(r,t,s),e,i),le=(t=10,e)=>{const s=e?tt:et;return e=>s(at(e,0,1)*t)*(1/t)},ce=(...t)=>{const e=t.length;if(!e)return oe;const s=e-1,i=t[0],r=t[s],n=[0],o=[q(i)];for(let e=1;e<s;e++){const i=t[e],r=M(i)?i.trim().split(" "):[i],a=r[0],h=r[1];n.push(z(h)?e/s:q(h)/100),o.push(q(a))}return o.push(q(r)),n.push(1),function(t){for(let e=1,s=n.length;e<s;e++){const s=n[e];if(t<=s){const i=n[e-1],r=o[e-1];return r+(o[e]-r)*(t-i)/(s-i)}}return o[o.length-1]}},de=(t=10,e=1)=>{const s=[0],i=t-1;for(let t=1;t<i;t++){const r=s[t-1],n=t/i,o=n*(1-e)+(n+((t+1)/i-n)*Math.random())*e;s.push(at(o,r,1))}return s.push(1),ce(...s)},ue=nt/2,pe=2*nt,me=(t=1.68)=>e=>Q(e,+t),fe={[p]:me,Quad:me(2),Cubic:me(3),Quart:me(4),Quint:me(5),Sine:t=>1-Z(t*ue),Circ:t=>1-j(1-t*t),Expo:t=>t?Q(2,10*t-10):0,Bounce:t=>{let e,s=4;for(;t<((e=Q(2,--s))-1)/11;);return 1/Q(4,3-s)-7.5625*Q((3*e-2)/22-t,2)},Back:(t=1.70158)=>e=>(+t+1)*e*e*e-+t*e*e,Elastic:(t=1,e=.3)=>{const s=at(+t,1,10),i=at(+e,c,2),r=i/pe*st(1/s),n=pe/i;return t=>0===t||1===t?t:-s*Q(2,-10*(1-t))*G((1-t-r)*n)}},ge={in:t=>e=>t(e),out:t=>e=>1-t(1-e),inOut:t=>e=>e<.5?t(2*e)/2:1-t(-2*e+2)/2,outIn:t=>e=>e<.5?(1-t(1-2*e))/2:(t(2*e-1)+1)/2},ye=(t,e,s)=>{if(s[t])return s[t];if(t.indexOf("(")<=-1){const i=ge[t]||t.includes("Back")||t.includes("Elastic")?e[t]():e[t];return i?s[t]=i:oe}{const i=t.slice(0,-1).split("("),r=e[i[0]];return r?s[t]=r(...i[1].split(",")):oe}},_e=(()=>{const t={linear:ce,irregular:de,steps:le,cubicBezier:he};for(let e in ge)for(let s in fe){const i=fe[s],r=ge[e];t[e+s]=s===p||"Back"===s||"Elastic"===s?(t,e)=>r(i(t,e)):r(i)}return t})(),ve={linear:oe},be=t=>I(t)?t:M(t)?ye(t,_e,ve):oe,xe={},Te=(t,e,s)=>{if(3===s)return m.get(t)||t;if(2===s||1===s&&X(e)&&t in e.style){const e=xe[t];if(e)return e;{const e=t?A(t):t;return xe[t]=e,e}}return t},we={deg:1,rad:180/nt,turn:360},Se={},ke=(t,e,i,r=!1)=>{const n=e.u,o=e.n;if(1===e.t&&n===i)return e;const a=o+n+i,h=Se[a];if(z(h)||r){let r;if(n in we)r=o*we[n]/we[i];else{const e=100,a=t.cloneNode(),h=t.parentNode,l=h&&h!==s?h:s.body;l.appendChild(a);const c=a.style;c.width=e+n;const d=a.offsetWidth||e;c.width=e+i;const u=d/(a.offsetWidth||e);l.removeChild(a),r=u*o}e.n=r,Se[a]=r}else e.n=h;return e.t,e.u=i,e},$e=t=>{if(t._hasChildren)_t(t,$e,!0);else{const e=t;e.pause(),_t(e,(t=>{const s=t.property,i=t.target;if(i[n]){const r=i.style,n=e._inlineStyles[s];if(3===t._tweenType){const e=i[a];if(z(n)||n===p?delete e[s]:e[s]=n,t._renderTransforms)if(Object.keys(e).length){let t=p;for(let s in e)t+=g[s]+e[s]+") ";r.transform=t}else r.removeProperty("transform")}else z(n)||n===p?r.removeProperty(s):r[s]=n;e._tail===t&&e.targets.forEach((t=>{t.getAttribute&&t.getAttribute("style")===p&&t.removeAttribute("style")}))}}))}return t},Ce={t:0,n:0,u:null,o:null,d:null,s:null},Ee={t:0,n:0,u:null,o:null,d:null,s:null},Be={func:null},Le=[null],De=[null,null],Ae={to:null};let Ne,Fe,Oe=0;class Pe extends ne{constructor(t,e,s,r,n=!1,o=0,a=0){super(e,s,r);const h=Ft(t),l=h.length,d=e.keyframes,p=d?yt(((t,e)=>{const s={};if(O(t)){const e=[].concat(...t.map((t=>Object.keys(t)))).filter(U);for(let i=0,r=e.length;i<r;i++){const r=e[i],n=t.map((t=>{const e={};for(let s in t){const i=t[s];U(s)?s===r&&(e.to=i):e[s]=i}return e}));s[r]=n}}else{const i=Yt(e.duration,L.defaults.duration),r=Object.keys(t).map((e=>({o:parseFloat(e)/100,p:t[e]}))).sort(((t,e)=>t.o-e.o));r.forEach((t=>{const e=t.o,r=t.p;for(let t in r)if(U(t)){let n=s[t];n||(n=s[t]=[]);const o=e*i;let a=n.length,h=n[a-1];const l={to:r[t]};let c=0;for(let t=0;t<a;t++)c+=n[t].duration;1===a&&(l.from=h.to),r.ease&&(l.ease=r.ease),l.duration=o-(a?c:0),n.push(l)}return t}));for(let t in s){const e=s[t];let i;for(let t=0,s=e.length;t<s;t++){const s=e[t],r=s.ease;s.ease=i||void 0,i=r}e[0].duration||e.shift()}}return s})(d,e),e):e,{delay:m,duration:f,ease:g,playbackEase:y,modifier:_,composition:v,onRender:b}=p,x=s?s.defaults:L.defaults,T=Yt(y,x.playbackEase),w=T?be(T):null,S=!z(g)&&!z(g.ease),k=S?g.ease:Yt(g,w?"linear":x.ease),$=S?g.duration:Yt(f,x.duration),C=Yt(m,x.delay),E=_||x.modifier,B=z(v)&&l>=u?i.none:z(v)?x.composition:v,D={},A=this._offset+(s?s._offset:0);let N=NaN,F=NaN,M=0,I=0;for(let t=0;t<l;t++){const e=h[t],r=o||t,d=a||l;let u=NaN,m=NaN;for(let t in p)if(U(t)){const o=Wt(e,t),a=Te(t,e,o);let h=p[t];const l=O(h);if(n&&!l&&(De[0]=h,De[1]=h,h=De),l){const t=h.length,e=!P(h[0]);2===t&&e?(Ae.to=h,Le[0]=Ae,Ne=Le):t>2&&e?(Ne=[],h.forEach(((t,e)=>{e?1===e?(De[1]=t,Ne.push(De)):Ne.push(t):De[0]=t}))):Ne=h}else Le[0]=h,Ne=Le;let f=null,g=null,y=NaN,_=0,v=0;for(let t=Ne.length;v<t;v++){const n=Ne[v];P(n)?Fe=n:(Ae.to=n,Fe=Ae),Be.func=null;const h=Xt(Fe.to,e,r,d,Be);let l;P(h)&&!z(h.to)?(Fe=h,l=h.to):l=h;const u=Xt(Fe.from,e,r,d),p=Fe.ease,m=!z(p)&&!z(p.ease),b=m?p.ease:p||k,x=m?p.duration:Xt(Yt(Fe.duration,t>1?Xt($,e,r,d)/t:$),e,r,d),T=Xt(Yt(Fe.delay,v?0:C),e,r,d),w=Xt(Yt(Fe.composition,B),e,r,d),S=R(w)?w:i[w],L=Fe.modifier||E,N=!z(u),F=!z(l),Y=O(l),X=Y||N&&F,W=g?_+T:T,V=lt(A+W,12);I||!N&&!Y||(I=1);let H=g;if(S!==i.none){f||(f=Zt(e,a));let t=f._head;for(;t&&!t._isOverridden&&t._absoluteStartTime<=V;)if(H=t,t=t._nextRep,t&&t._absoluteStartTime>=V)for(;t;)Kt(t),t=t._nextRep}if(X?(qt(Y?Xt(l[0],e,r,d):u,Ce),qt(Y?Xt(l[1],e,r,d,Be):l,Ee),0===Ce.t&&(H?1===H._valueType&&(Ce.t=1,Ce.u=H._unit):(qt(Ht(e,a,o,D),jt),1===jt.t&&(Ce.t=1,Ce.u=jt.u)))):(F?qt(l,Ee):g?Qt(g,Ee):qt(s&&H&&H.parent.parent===s?H._value:Ht(e,a,o,D),Ee),N?qt(u,Ce):g?Qt(g,Ce):qt(s&&H&&H.parent.parent===s?H._value:Ht(e,a,o,D),Ce)),Ce.o&&(Ce.n=Ut(H?H._toNumber:qt(Ht(e,a,o,D),jt).n,Ce.n,Ce.o)),Ee.o&&(Ee.n=Ut(Ce.n,Ee.n,Ee.o)),Ce.t!==Ee.t)if(3===Ce.t||3===Ee.t){const t=3===Ce.t?Ce:Ee,e=3===Ce.t?Ee:Ce;e.t=3,e.s=gt(t.s),e.d=t.d.map((()=>e.n))}else if(1===Ce.t||1===Ee.t){const t=1===Ce.t?Ce:Ee,e=1===Ce.t?Ee:Ce;e.t=1,e.u=t.u}else if(2===Ce.t||2===Ee.t){const t=2===Ce.t?Ce:Ee,e=2===Ce.t?Ee:Ce;e.t=2,e.s=t.s,e.d=[0,0,0,1]}if(Ce.u!==Ee.u){let t=Ee.u?Ce:Ee;t=ke(e,t,Ee.u?Ee.u:Ce.u,!1)}if(Ee.d&&Ce.d&&Ee.d.length!==Ce.d.length){const t=Ce.d.length>Ee.d.length?Ce:Ee,e=t===Ce?Ee:Ce;e.d=t.d.map(((t,s)=>z(e.d[s])?0:e.d[s])),e.s=gt(t.s)}const U=lt(+x||c,12),q={parent:this,id:Oe++,property:a,target:e,_value:null,_func:Be.func,_ease:be(b),_fromNumbers:gt(Ce.d),_toNumbers:gt(Ee.d),_strings:gt(Ee.s),_fromNumber:Ce.n,_toNumber:Ee.n,_numbers:gt(Ce.d),_number:Ce.n,_unit:Ee.u,_modifier:L,_currentTime:0,_startTime:W,_delay:+T,_updateDuration:U,_changeDuration:U,_absoluteStartTime:V,_tweenType:o,_valueType:Ee.t,_composition:S,_isOverlapped:0,_isOverridden:0,_renderTransforms:0,_prevRep:null,_nextRep:null,_prevAdd:null,_nextAdd:null,_prev:null,_next:null};S!==i.none&&te(q,f),isNaN(y)&&(y=q._startTime),_=lt(W+U,12),g=q,M++,bt(this,q)}(isNaN(F)||y<F)&&(F=y),(isNaN(N)||_>N)&&(N=_),3===o&&(u=M-v,m=M)}if(!isNaN(u)){let t=0;_t(this,(e=>{t>=u&&t<m&&(e._renderTransforms=1,e._composition===i.blend&&_t(kt.animation,(t=>{t.id===e.id&&(t._renderTransforms=1)}))),t++}))}}l||console.warn("No target found. Make sure the element you're trying to animate is accessible before creating your animation."),F?(_t(this,(t=>{t._startTime-t._delay||(t._delay-=F),t._startTime-=F})),N-=F):F=0,N||(N=c,this.iterationCount=0),this.targets=h,this.duration=N===c?c:mt((N+this._loopDelay)*this.iterationCount-this._loopDelay)||c,this.onRender=b||x.onRender,this._ease=w,this._delay=F,this.iterationDuration=N,this._inlineStyles=D,!this._autoplay&&I&&this.onRender(this)}stretch(t){const e=this.duration;if(e===ft(t))return this;const s=t/e;return _t(this,(t=>{t._updateDuration=ft(t._updateDuration*s),t._changeDuration=ft(t._changeDuration*s),t._currentTime*=s,t._startTime*=s,t._absoluteStartTime*=s})),super.stretch(t)}refresh(){return _t(this,(t=>{const e=t._func;if(e){const s=Ht(t.target,t.property,t._tweenType);qt(s,jt),qt(e(),Ee),t._fromNumbers=gt(jt.d),t._fromNumber=jt.n,t._toNumbers=gt(Ee.d),t._strings=gt(Ee.s),t._toNumber=Ee.o?Ut(jt.n,Ee.n,Ee.o):Ee.n}})),this}revert(){return super.revert(),$e(this)}then(t){return super.then(t)}}const Re=(t,e=100)=>{const s=[];for(let i=0;i<=e;i++)s.push(t(i/e));return`linear(${s.join(", ")})`},Me={in:"ease-in",out:"ease-out",inOut:"ease-in-out"},Ie=(()=>{const t={};for(let e in ge)t[e]=t=>ge[e](me(t));return t})(),ze=t=>{let e=Me[t];if(e)return e;if(e="linear",M(t)){if(N(t,"linear")||N(t,"cubic-")||N(t,"steps")||N(t,"ease"))e=t;else if(N(t,"cubicB"))e=A(t);else{const s=ye(t,Ie,Me);I(s)&&(e=s===oe?"linear":Re(s))}Me[t]=e}else if(I(t)){const s=Re(t);s&&(e=s)}else t.ease&&(e=Re(t.ease));return e},Ye=["x","y","z"],Xe=["perspective","width","height","margin","padding","top","right","bottom","left","borderWidth","fontSize","borderRadius",...Ye],We=(()=>[...Ye,...f.filter((t=>["X","Y","Z"].some((e=>t.endsWith(e)))))])();let Ve=null;const He={_head:null,_tail:null},Ue=(t,e,s)=>{let i=He._head;for(;i;){const r=i._next,n=i.$el===t,o=!e||i.property===e,a=!s||i.parent===s;if(n&&o&&a){const t=i.animation;try{t.commitStyles()}catch{}t.cancel(),vt(He,i);const e=i.parent;e&&(e._completed++,e.animations.length===e._completed&&(e.completed=!0,e.muteCallbacks||(e.paused=!0,e.onComplete(e),e._resolve(e))))}i=r}},qe=(t,e,s,i,r)=>{const n=e.animate(i,r),o=r.delay+ +r.duration*r.iterations;n.playbackRate=t._speed,t.paused&&n.pause(),t.duration<o&&(t.duration=o,t.controlAnimation=n),t.animations.push(n),Ue(e,s),bt(He,{parent:t,animation:n,$el:e,property:s,_next:null,_prev:null});const a=()=>{Ue(e,s,t)};return n.onremove=a,n.onfinish=a,n},Qe=(t,e,s,i,r)=>{let n=Xt(e,s,i,r);return R(n)?Xe.includes(t)||N(t,"translate")?`${n}px`:N(t,"rotate")||N(t,"skew")?`${n}deg`:`${n}`:n},je=(t,e,s,i,r,n)=>{let o="0";const a=z(i)?getComputedStyle(t)[e]:Qe(e,i,t,r,n);return o=z(s)?O(i)?i.map((s=>Qe(e,s,t,r,n))):a:[Qe(e,s,t,r,n),a],o};class Ge{constructor(e,s){B.current&&B.current.register(this),Y(Ve)&&(!t||!z(CSS)&&Object.hasOwnProperty.call(CSS,"registerProperty")?(f.forEach((t=>{const e=N(t,"skew"),s=N(t,"scale"),i=N(t,"rotate"),r=N(t,"translate"),n=i||e,o=n?"<angle>":s?"<number>":r?"<length-percentage>":"*";try{CSS.registerProperty({name:"--"+t,syntax:o,inherits:!1,initialValue:r?"0px":n?"0deg":s?"1":"0"})}catch{}})),Ve=!0):Ve=!1);const i=Ft(e),r=i.length;r||console.warn("No target found. Make sure the element you're trying to animate is accessible before creating your animation.");const n=Yt(s.ease,ze(L.defaults.ease)),o=n.ease&&n,h=Yt(s.autoplay,L.defaults.autoplay),l=!(!h||!h.link)&&h,c=s.alternate&&!0===s.alternate,d=s.reversed&&!0===s.reversed,_=Yt(s.loop,L.defaults.loop),v=!0===_||_===1/0?1/0:R(_)?_+1:1,b=c?d?"alternate-reverse":"alternate":d?"reverse":"normal",x=ze(n),T=1===L.timeScale?1:u;this.targets=i,this.animations=[],this.controlAnimation=null,this.onComplete=s.onComplete||y,this.duration=0,this.muteCallbacks=!1,this.completed=!1,this.paused=!h||!1!==l,this.reversed=d,this.autoplay=h,this._speed=Yt(s.playbackRate,L.defaults.playbackRate),this._resolve=y,this._completed=0,this._inlineStyles=i.map((t=>t.getAttribute("style"))),i.forEach(((t,e)=>{const i=t[a],h=We.some((t=>s.hasOwnProperty(t))),l=(o?o.duration:Xt(Yt(s.duration,L.defaults.duration),t,e,r))*T,c=Xt(Yt(s.delay,L.defaults.delay),t,e,r)*T,d=Yt(s.composition,"replace");for(let o in s){if(!U(o))continue;const a={},u={iterations:v,direction:b,fill:"forwards",easing:x,duration:l,delay:c,composite:d},p=s[o],g=!!h&&(f.includes(o)?o:m.get(o));let y;if(P(p)){const s=p,h=Yt(s.ease,n),m=h.ease&&h,f=s.to,_=s.from;if(u.duration=(m?m.duration:Xt(Yt(s.duration,l),t,e,r))*T,u.delay=Xt(Yt(s.delay,c),t,e,r)*T,u.composite=Yt(s.composition,d),u.easing=ze(h),y=je(t,o,_,f,e,r),g?(a[`--${g}`]=y,i[g]=y):a[o]=je(t,o,_,f,e,r),qe(this,t,o,a,u),!z(_))if(g){const e=`--${g}`;t.style.setProperty(e,a[e][0])}else t.style[o]=a[o][0]}else y=O(p)?p.map((s=>Qe(o,s,t,e,r))):Qe(o,p,t,e,r),g?(a[`--${g}`]=y,i[g]=y):a[o]=y,qe(this,t,o,a,u)}if(h){let e=p;for(let t in i)e+=`${g[t]}var(--${t})) `;t.style.transform=e}})),l&&this.autoplay.link(this)}forEach(t){const e=M(t)?e=>e[t]():t;return this.animations.forEach(e),this}get speed(){return this._speed}set speed(t){this._speed=+t,this.forEach((e=>e.playbackRate=t))}get currentTime(){const t=this.controlAnimation,e=L.timeScale;return this.completed?this.duration:t?+t.currentTime*(1===e?1:e):0}set currentTime(t){const e=t*(1===L.timeScale?1:u);this.forEach((t=>{e>=this.duration&&t.play(),t.currentTime=e}))}get progress(){return this.currentTime/this.duration}set progress(t){this.forEach((e=>e.currentTime=t*this.duration||0))}resume(){return this.paused?(this.paused=!1,this.forEach("play")):this}pause(){return this.paused?this:(this.paused=!0,this.forEach("pause"))}alternate(){return this.reversed=!this.reversed,this.forEach("reverse"),this.paused&&this.forEach("pause"),this}play(){return this.reversed&&this.alternate(),this.resume()}reverse(){return this.reversed||this.alternate(),this.resume()}seek(t,e=!1){return e&&(this.muteCallbacks=!0),t<this.duration&&(this.completed=!1),this.currentTime=t,this.muteCallbacks=!1,this.paused&&this.pause(),this}restart(){return this.completed=!1,this.seek(0,!0).resume()}commitStyles(){return this.forEach("commitStyles")}complete(){return this.seek(this.duration)}cancel(){return this.forEach("cancel"),this.pause()}revert(){return this.cancel(),this.targets.forEach(((t,e)=>t.setAttribute("style",this._inlineStyles[e]))),this}then(t=y){const e=this.then,s=()=>{this.then=null,t(this),this.then=e,this._resolve=y};return new Promise((t=>(this._resolve=()=>t(s()),this.completed&&this._resolve(),this)))}}const Ze={animate:(t,e)=>new Ge(t,e),convertEase:Re},Je=(t=y)=>new ne({duration:1*L.timeScale,onComplete:t},null,0).resume();function Ke(t,e,s){const i=Ft(t);if(!i.length)return;const[r]=i,n=Wt(r,e),o=Te(e,r,n);let a=Ht(r,o);if(z(s))return a;if(qt(a,jt),0===jt.t||1===jt.t){if(!1===s)return jt.n;{const t=ke(r,jt,s,!1);return`${lt(t.n,L.precision)}${t.u}`}}}const ts=(t,e)=>{if(!z(e))return e.duration=c,e.composition=Yt(e.composition,i.none),new Pe(t,e,null,0,!0).resume()},es=(t,e,s)=>{let i=!1;return _t(e,(r=>{const n=r.target;if(t.includes(n)){const t=r.property,o=r._tweenType,a=Te(s,n,o);(!a||a&&a===t)&&(r.parent._tail===r&&3===r._tweenType&&r._prev&&3===r._prev._tweenType&&(r._prev._renderTransforms=1),vt(e,r),ee(r),i=!0)}}),!0),i},ss=(t,e,s)=>{const i=Nt(t),r=e||Bt,n=e&&e.controlAnimation&&e;for(let t=0,e=i.length;t<e;t++){const e=i[t];Ue(e,s,n)}let o;if(r._hasChildren){let e=0;_t(r,(n=>{if(!n._hasChildren)if(o=es(i,n,s),o&&!n._head)n.cancel(),vt(r,n);else{const t=n._offset+n._delay+n.duration;t>e&&(e=t)}n._head?ss(t,n,s):n._hasChildren=!1}),!0),z(r.iterationDuration)||(r.iterationDuration=e)}else o=es(i,r,s);return o&&!r._head&&(r._hasChildren=!1,r.cancel&&r.cancel()),i},is=(t,e,s,i,r)=>i+(t-e)/(s-e)*(r-i),rs=(t,e,s,i)=>{let r=u/L.defaults.frameRate;if(!1!==i){const t=i||Bt._hasChildren&&Bt;t&&t.deltaTime&&(r=t.deltaTime)}const n=1-Math.exp(-s*r*.1);return s?1===s?e:(1-n)*t+n*e:t},ns=t=>(...e)=>{const s=t(...e);return new Proxy(y,{apply:(t,e,[i])=>s(i),get:(t,e)=>ns(((...t)=>{const i=as[e](...t);return t=>i(s(t))}))})},os=(t,e=0)=>(...s)=>(s.length<t.length?ns(((t,e=0)=>(...s)=>e?e=>t(...s,e):e=>t(e,...s))(t,e)):t)(...s),as={$:Ft,get:Ke,set:ts,remove:ss,cleanInlineStyles:$e,random:ut,randomPick:t=>t[ut(0,t.length-1)],shuffle:pt,lerp:rs,sync:Je,keepTime:xt,clamp:os(at),round:os(lt),snap:os(ct),wrap:os(((t,e,s)=>((t-e)%(s-e)+(s-e))%(s-e)+e)),interpolate:os(dt,1),mapRange:os(is),roundPad:os(((t,e)=>(+t).toFixed(e))),padStart:os(((t,e,s)=>`${t}`.padStart(e,s))),padEnd:os(((t,e,s)=>`${t}`.padEnd(e,s))),degToRad:os((t=>t*nt/180)),radToDeg:os((t=>180*t/nt))},hs=(t,e)=>{let s=t.iterationDuration;if(s===c&&(s=0),z(e))return s;if(R(+e))return+e;const i=e,r=t?t.labels:null,n=!Y(r),o=((t,e)=>{if(N(e,"<")){const s="<"===e[1],i=t._tail,r=i?i._offset+i._delay:0;return s?r:r+i.duration}})(t,i),a=!z(o),h=C.exec(i);if(h){const t=h[0],e=i.split(t),l=n&&e[0]?r[e[0]]:s,c=a?o:n?l:s,d=+e[1];return Ut(c,d,t[0])}return a?o:n?z(r[i])?s:r[i]:s};function ls(t,e,s,i,r,n){const o=R(t.duration)&&t.duration<=c?s-c:s;St(e,o,1,1,1);const a=i?new Pe(i,t,e,o,!1,r,n):new ne(t,e,o);return a.init(1),bt(e,a),_t(e,(t=>{const s=t._offset+t._delay+t.duration;s>e.iterationDuration&&(e.iterationDuration=s)})),e.duration=function(t){return mt((t.iterationDuration+t._loopDelay)*t.iterationCount-t._loopDelay)||c}(e),e}class cs extends ne{constructor(t={}){super(t,null,0),this.duration=0,this.labels={};const e=t.defaults,s=L.defaults;this.defaults=e?yt(e,s):s,this.onRender=t.onRender||s.onRender;const i=Yt(t.playbackEase,s.playbackEase);this._ease=i?be(i):null,this.iterationDuration=0}add(t,e,s){const i=P(e),r=P(t);if(i||r){if(this._hasChildren=!0,i){const i=e;if(I(s)){const e=s,r=Nt(t),n=this.duration,o=this.iterationDuration,a=i.id;let h=0;const l=r.length;r.forEach((t=>{const s={...i};this.duration=n,this.iterationDuration=o,z(a)||(s.id=a+"-"+h),ls(s,this,hs(this,e(t,h,l,this)),t,h,l),h++}))}else ls(i,this,hs(this,s),t)}else ls(t,this,hs(this,e));return this.init(1)}}sync(t,e){if(z(t)||t&&z(t.pause))return this;t.pause();const s=+(t.effect?t.effect.getTiming().duration:t.duration);return this.add(t,{currentTime:[0,s],duration:s,ease:"linear"},e)}set(t,e,s){return z(e)?this:(e.duration=c,e.composition=i.replace,this.add(t,e,s))}call(t,e){return z(t)||t&&!I(t)?this:this.add({duration:0,onComplete:()=>t(this)},e)}label(t,e){return z(t)||t&&!M(t)||(this.labels[t]=hs(this,e)),this}remove(t,e){return ss(t,this,e),this}stretch(t){const e=this.duration;if(e===ft(t))return this;const s=t/e,i=this.labels;_t(this,(t=>t.stretch(t.duration*s)));for(let t in i)i[t]*=s;return super.stretch(t)}refresh(){return _t(this,(t=>{t.refresh&&t.refresh()})),this}revert(){return super.revert(),_t(this,(t=>t.revert),!0),$e(this)}then(t){return super.then(t)}}class ds{constructor(t,e){B.current&&B.current.register(this);const s=()=>{if(this.callbacks.completed)return;let t=!0;for(let e in this.animations)if(!this.animations[e].paused&&t){t=!1;break}t&&this.callbacks.complete()},r={onBegin:()=>{this.callbacks.completed&&this.callbacks.reset(),this.callbacks.play()},onComplete:s,onPause:s},n={v:1,autoplay:!1},o={};if(this.targets=[],this.animations={},this.callbacks=null,!z(t)&&!z(e)){for(let t in e){const s=e[t];U(t)?o[t]=s:N(t,"on")?n[t]=s:r[t]=s}this.callbacks=new Pe({v:0},n);for(let e in o){const s=o[e],n=P(s);let a={},h="+=0";if(n){const t=s.unit;M(t)&&(h+=t)}else a.duration=s;a[e]=n?yt({to:h},s):h;const l=yt(r,a);l.composition=i.replace,l.autoplay=!1;const c=this.animations[e]=new Pe(t,l,null,0,!1).init();this.targets.length||this.targets.push(...c.targets),this[e]=(t,e,s)=>{const i=c._head;if(z(t)&&i){const t=i._numbers;return t&&t.length?t:i._modifier(i._number)}return _t(c,(e=>{if(O(t))for(let s=0,i=t.length;s<i;s++)z(e._numbers[s])||(e._fromNumbers[s]=e._modifier(e._numbers[s]),e._toNumbers[s]=t[s]);else e._fromNumber=e._modifier(e._number),e._toNumber=t;z(s)||(e._ease=be(s)),e._currentTime=0})),z(e)||c.stretch(e),c.reset(1).resume(),this}}}}revert(){for(let t in this.animations)this[t]=y,this.animations[t].revert();return this.animations={},this.targets.length=0,this.callbacks&&this.callbacks.revert(),this}}const us=10*u;class ps{constructor(t={}){this.timeStep=.02,this.restThreshold=5e-4,this.restDuration=200,this.maxDuration=6e4,this.maxRestSteps=this.restDuration/this.timeStep/u,this.maxIterations=this.maxDuration/this.timeStep/u,this.m=at(Yt(t.mass,1),0,us),this.s=at(Yt(t.stiffness,100),1,us),this.d=at(Yt(t.damping,10),.1,us),this.v=at(Yt(t.velocity,0),-1e4,us),this.w0=0,this.zeta=0,this.wd=0,this.b=0,this.solverDuration=0,this.duration=0,this.compute(),this.ease=t=>0===t||1===t?t:this.solve(t*this.solverDuration)}solve(t){const{zeta:e,w0:s,wd:i,b:r}=this;let n=t;return n=e<1?K(-n*e*s)*(1*Z(i*n)+r*G(i*n)):(1+r*n)*K(-n*s),1-n}compute(){const{maxRestSteps:t,maxIterations:e,restThreshold:s,timeStep:i,m:r,d:n,s:o,v:a}=this,h=this.w0=at(j(o/r),c,u),l=this.zeta=n/(2*j(o*r)),d=this.wd=l<1?h*j(1-l*l):0;this.b=l<1?(l*h-a)/d:-a+h;let p=0,m=0,f=0;for(;m<t&&f<e;)J(1-this.solve(p))<s?m++:m=0,this.solverDuration=p,p+=i,f++;this.duration=lt(this.solverDuration*u,0)*L.timeScale}get mass(){return this.m}set mass(t){this.m=at(Yt(t,1),0,us),this.compute()}get stiffness(){return this.s}set stiffness(t){this.s=at(Yt(t,100),1,us),this.compute()}get damping(){return this.d}set damping(t){this.d=at(Yt(t,10),.1,us),this.compute()}get velocity(){return this.v}set velocity(t){this.v=at(Yt(t,0),-1e4,us),this.compute()}}const ms=t=>new ps(t),fs=t=>{t.cancelable&&t.preventDefault()};class gs{constructor(t){this.el=t,this.zIndex=0,this.parentElement=null,this.classList={add:y,remove:y}}get x(){return this.el.x||0}set x(t){this.el.x=t}get y(){return this.el.y||0}set y(t){this.el.y=t}get width(){return this.el.width||0}set width(t){this.el.width=t}get height(){return this.el.height||0}set height(t){this.el.height=t}getBoundingClientRect(){return{top:this.y,right:this.x,bottom:this.y+this.height,left:this.x+this.width}}}class ys{constructor(t){this.$el=t,this.inlineTransforms=[],this.point=new DOMPoint,this.inversedMatrix=this.getMatrix().inverse()}normalizePoint(t,e){return this.point.x=t,this.point.y=e,this.point.matrixTransform(this.inversedMatrix)}traverseUp(t){let e=this.$el.parentElement,i=0;for(;e&&e!==s;)t(e,i),e=e.parentElement,i++}getMatrix(){const t=new DOMMatrix;return this.traverseUp((e=>{const s=getComputedStyle(e).transform;if(s){const e=new DOMMatrix(s);t.preMultiplySelf(e)}})),t}remove(){this.traverseUp(((t,e)=>{this.inlineTransforms[e]=t.style.transform,t.style.transform="none"}))}revert(){this.traverseUp(((t,e)=>{const s=this.inlineTransforms[e];""===s?t.style.removeProperty("transform"):t.style.transform=s}))}}const _s=(t,e)=>t&&I(t)?t(e):t;let vs=0;class bs{constructor(t,i={}){if(!t)return;B.current&&B.current.register(this);const r=i.x,n=i.y,o=i.trigger,a=i.modifier,h=i.releaseEase,l=h&&be(h),c=!z(h)&&!z(h.ease),u=P(r)&&!z(r.mapTo)?r.mapTo:"translateX",p=P(n)&&!z(n.mapTo)?n.mapTo:"translateY",m=_s(i.container,this);this.containerArray=O(m)?m:null,this.$container=m&&!this.containerArray?Nt(m)[0]:s.body,this.useWin=this.$container===s.body,this.$scrollContainer=this.useWin?e:this.$container,this.$target=P(t)?new gs(t):Nt(t)[0],this.$trigger=Nt(o||t)[0],this.fixed="fixed"===Ke(this.$target,"position"),this.isFinePointer=!0,this.containerPadding=[0,0,0,0],this.containerFriction=0,this.releaseContainerFriction=0,this.snapX=0,this.snapY=0,this.scrollSpeed=0,this.scrollThreshold=0,this.dragSpeed=0,this.maxVelocity=0,this.minVelocity=0,this.velocityMultiplier=0,this.cursor=!1,this.releaseXSpring=c?h:ms({mass:Yt(i.releaseMass,1),stiffness:Yt(i.releaseStiffness,80),damping:Yt(i.releaseDamping,20)}),this.releaseYSpring=c?h:ms({mass:Yt(i.releaseMass,1),stiffness:Yt(i.releaseStiffness,80),damping:Yt(i.releaseDamping,20)}),this.releaseEase=l||_e.outQuint,this.hasReleaseSpring=c,this.onGrab=i.onGrab||y,this.onDrag=i.onDrag||y,this.onRelease=i.onRelease||y,this.onUpdate=i.onUpdate||y,this.onSettle=i.onSettle||y,this.onSnap=i.onSnap||y,this.onResize=i.onResize||y,this.onAfterResize=i.onAfterResize||y,this.disabled=[0,0];const f={};if(a&&(f.modifier=a),z(r)||!0===r)f[u]=0;else if(P(r)){const t=r,e={};t.modifier&&(e.modifier=t.modifier),t.composition&&(e.composition=t.composition),f[u]=e}else!1===r&&(f[u]=0,this.disabled[0]=1);if(z(n)||!0===n)f[p]=0;else if(P(n)){const t=n,e={};t.modifier&&(e.modifier=t.modifier),t.composition&&(e.composition=t.composition),f[p]=e}else!1===n&&(f[p]=0,this.disabled[1]=1);this.animate=new ds(this.$target,f),this.xProp=u,this.yProp=p,this.destX=0,this.destY=0,this.deltaX=0,this.deltaY=0,this.scroll={x:0,y:0},this.coords=[this.x,this.y,0,0],this.snapped=[0,0],this.pointer=[0,0,0,0,0,0,0,0],this.scrollView=[0,0],this.dragArea=[0,0,0,0],this.containerBounds=[-1e12,d,d,-1e12],this.scrollBounds=[0,0,0,0],this.targetBounds=[0,0,0,0],this.window=[0,0],this.velocityStack=[0,0,0],this.velocityStackIndex=0,this.velocityTime=F(),this.velocity=0,this.angle=0,this.cursorStyles=null,this.triggerStyles=null,this.bodyStyles=null,this.targetStyles=null,this.touchActionStyles=null,this.transforms=new ys(this.$target),this.overshootCoords={x:0,y:0},this.overshootTicker=new ne({autoplay:!1,onUpdate:()=>{this.updated=!0,this.manual=!0,this.disabled[0]||this.animate[this.xProp](this.overshootCoords.x,1),this.disabled[1]||this.animate[this.yProp](this.overshootCoords.y,1)},onComplete:()=>{this.manual=!1,this.disabled[0]||this.animate[this.xProp](this.overshootCoords.x,0),this.disabled[1]||this.animate[this.yProp](this.overshootCoords.y,0)}},null,0).init(),this.updateTicker=new ne({autoplay:!1,onUpdate:()=>this.update()},null,0).init(),this.contained=!z(m),this.manual=!1,this.grabbed=!1,this.dragged=!1,this.updated=!1,this.released=!1,this.canScroll=!1,this.enabled=!1,this.initialized=!1,this.activeProp=this.disabled[1]?u:p,this.animate.callbacks.onRender=()=>{const t=this.updated,e=!(this.grabbed&&t)&&this.released,s=this.x,i=this.y,r=s-this.coords[2],n=i-this.coords[3];this.deltaX=r,this.deltaY=n,this.coords[2]=s,this.coords[3]=i,t&&(r||n)&&this.onUpdate(this),e?(this.computeVelocity(r,n),this.angle=rt(n,r)):this.updated=!1},this.animate.callbacks.onComplete=()=>{!this.grabbed&&this.released&&(this.released=!1),this.manual||(this.deltaX=0,this.deltaY=0,this.velocity=0,this.velocityStack[0]=0,this.velocityStack[1]=0,this.velocityStack[2]=0,this.velocityStackIndex=0,this.onSettle(this))},this.resizeTicker=new ne({autoplay:!1,duration:150*L.timeScale,onComplete:()=>{this.onResize(this),this.refresh(),this.onAfterResize(this)}}).init(),this.parameters=i,this.resizeObserver=new ResizeObserver((()=>{this.initialized?this.resizeTicker.restart():this.initialized=!0})),this.enable(),this.refresh(),this.resizeObserver.observe(this.$container),P(t)||this.resizeObserver.observe(this.$target)}computeVelocity(t,e){const s=this.velocityTime,i=F(),r=i-s;if(r<17)return this.velocity;this.velocityTime=i;const n=this.velocityStack,o=this.velocityMultiplier,a=this.minVelocity,h=this.maxVelocity,l=this.velocityStackIndex;n[l]=lt(at(j(t*t+e*e)/r*o,a,h),5);const c=it(n[0],n[1],n[2]);return this.velocity=c,this.velocityStackIndex=(l+1)%3,c}setX(t,e=!1){if(this.disabled[0])return;const s=lt(t,5);return this.overshootTicker.pause(),this.manual=!0,this.updated=!e,this.destX=s,this.snapped[0]=ct(s,this.snapX),this.animate[this.xProp](s,0),this.manual=!1,this}setY(t,e=!1){if(this.disabled[1])return;const s=lt(t,5);return this.overshootTicker.pause(),this.manual=!0,this.updated=!e,this.destY=s,this.snapped[1]=ct(s,this.snapY),this.animate[this.yProp](s,0),this.manual=!1,this}get x(){return lt(this.animate[this.xProp](),L.precision)}set x(t){this.setX(t,!1)}get y(){return lt(this.animate[this.yProp](),L.precision)}set y(t){this.setY(t,!1)}get progressX(){return is(this.x,this.containerBounds[3],this.containerBounds[1],0,1)}set progressX(t){this.setX(is(t,0,1,this.containerBounds[3],this.containerBounds[1]),!1)}get progressY(){return is(this.y,this.containerBounds[0],this.containerBounds[2],0,1)}set progressY(t){this.setY(is(t,0,1,this.containerBounds[0],this.containerBounds[2]),!1)}updateScrollCoords(){const t=lt(this.useWin?e.scrollX:this.$container.scrollLeft,0),s=lt(this.useWin?e.scrollY:this.$container.scrollTop,0),[i,r,n,o]=this.containerPadding,a=this.scrollThreshold;this.scroll.x=t,this.scroll.y=s,this.scrollBounds[0]=s-this.targetBounds[0]+i-a,this.scrollBounds[1]=t-this.targetBounds[1]-r+a,this.scrollBounds[2]=s-this.targetBounds[2]-n+a,this.scrollBounds[3]=t-this.targetBounds[3]+o-a}updateBoundingValues(){const t=this.$container;if(!t)return;const i=this.x,r=this.y,n=this.coords[2],o=this.coords[3];this.coords[2]=0,this.coords[3]=0,this.setX(0,!0),this.setY(0,!0),this.transforms.remove();const a=this.window[0]=e.innerWidth,h=this.window[1]=e.innerHeight,l=this.useWin,c=t.scrollWidth,d=t.scrollHeight,u=this.fixed,p=t.getBoundingClientRect(),[m,f,g,y]=this.containerPadding;this.dragArea[0]=l?0:p.left,this.dragArea[1]=l?0:p.top,this.scrollView[0]=l?at(c,a,c):c,this.scrollView[1]=l?at(d,h,d):d,this.updateScrollCoords();const{width:_,height:v,left:b,top:x,right:T,bottom:w}=t.getBoundingClientRect();this.dragArea[2]=lt(l?at(_,a,a):_,0),this.dragArea[3]=lt(l?at(v,h,h):v,0);const S=Ke(t,"overflow"),k="visible"===S,$="hidden"===S;if(this.canScroll=!u&&this.contained&&(t===s.body&&k||!$&&!k)&&(c>this.dragArea[2]+y-f||d>this.dragArea[3]+m-g)&&(!this.containerArray||this.containerArray&&!O(this.containerArray)),this.contained){const e=this.scroll.x,s=this.scroll.y,i=this.canScroll,r=this.$target.getBoundingClientRect(),n=i?l?0:t.scrollLeft:0,o=i?l?0:t.scrollTop:0,c=i?this.scrollView[0]-n-_:0,d=i?this.scrollView[1]-o-v:0;this.targetBounds[0]=lt(r.top+s-(l?0:x),0),this.targetBounds[1]=lt(r.right+e-(l?a:T),0),this.targetBounds[2]=lt(r.bottom+s-(l?h:w),0),this.targetBounds[3]=lt(r.left+e-(l?0:b),0),this.containerArray?(this.containerBounds[0]=this.containerArray[0]+m,this.containerBounds[1]=this.containerArray[1]-f,this.containerBounds[2]=this.containerArray[2]-g,this.containerBounds[3]=this.containerArray[3]+y):(this.containerBounds[0]=-lt(r.top-(u?at(x,0,h):x)+o-m,0),this.containerBounds[1]=-lt(r.right-(u?at(T,0,a):T)-c+f,0),this.containerBounds[2]=-lt(r.bottom-(u?at(w,0,h):w)-d+g,0),this.containerBounds[3]=-lt(r.left-(u?at(b,0,a):b)+n-y,0))}this.transforms.revert(),this.coords[2]=n,this.coords[3]=o,this.setX(i,!0),this.setY(r,!0)}isOutOfBounds(t,e,s){if(!this.contained)return 0;const[i,r,n,o]=t,[a,h]=this.disabled,l=!a&&e<o||!a&&e>r,c=!h&&s<i||!h&&s>n;return l&&!c?1:!l&&c?2:l&&c?3:0}refresh(){const t=this.parameters,i=t.x,r=t.y,n=_s(t.container,this),o=_s(t.containerPadding,this)||0,a=O(o)?o:[o,o,o,o],h=this.x,l=this.y,c=_s(t.cursor,this),d={onHover:"grab",onGrab:"grabbing"};if(c){const{onHover:t,onGrab:e}=c;t&&(d.onHover=t),e&&(d.onGrab=e)}this.containerArray=O(n)?n:null,this.$container=n&&!this.containerArray?Nt(n)[0]:s.body,this.useWin=this.$container===s.body,this.$scrollContainer=this.useWin?e:this.$container,this.isFinePointer=matchMedia("(pointer:fine)").matches,this.containerPadding=Yt(a,[0,0,0,0]),this.containerFriction=at(Yt(_s(t.containerFriction,this),.8),0,1),this.releaseContainerFriction=at(Yt(_s(t.releaseContainerFriction,this),this.containerFriction),0,1),this.snapX=_s(P(i)&&!z(i.snap)?i.snap:t.snap,this),this.snapY=_s(P(r)&&!z(r.snap)?r.snap:t.snap,this),this.scrollSpeed=Yt(_s(t.scrollSpeed,this),1.5),this.scrollThreshold=Yt(_s(t.scrollThreshold,this),20),this.dragSpeed=Yt(_s(t.dragSpeed,this),1),this.minVelocity=Yt(_s(t.minVelocity,this),0),this.maxVelocity=Yt(_s(t.maxVelocity,this),50),this.velocityMultiplier=Yt(_s(t.velocityMultiplier,this),1),this.cursor=!1!==c&&d,this.updateBoundingValues();const[u,p,m,f]=this.containerBounds;this.setX(at(h,f,p),!0),this.setY(at(l,u,m),!0)}update(){if(this.updateScrollCoords(),this.canScroll){const[t,e,s,i]=this.containerPadding,[r,n]=this.scrollView,o=this.dragArea[2],a=this.dragArea[3],h=this.scroll.x,l=this.scroll.y,c=this.$container.scrollWidth,d=this.$container.scrollHeight,u=this.useWin?at(c,this.window[0],c):c,p=this.useWin?at(d,this.window[1],d):d,m=r-u,f=n-p;this.dragged&&m>0&&(this.coords[0]-=m,this.scrollView[0]=u),this.dragged&&f>0&&(this.coords[1]-=f,this.scrollView[1]=p);const g=10*this.scrollSpeed,y=this.scrollThreshold,[_,v]=this.coords,[b,x,T,w]=this.scrollBounds,S=lt(at((v-b+t)/y,-1,0)*g,0),k=lt(at((_-x-e)/y,0,1)*g,0),$=lt(at((v-T-s)/y,0,1)*g,0),C=lt(at((_-w+i)/y,-1,0)*g,0);if(S||$||C||k){const[t,e]=this.disabled;let s=h,i=l;t||(s=lt(at(h+(C||k),0,r-o),0),this.coords[0]-=h-s),e||(i=lt(at(l+(S||$),0,n-a),0),this.coords[1]-=l-i),this.useWin?this.$scrollContainer.scrollBy(-(h-s),-(l-i)):this.$scrollContainer.scrollTo(s,i)}}const[t,e,s,i]=this.containerBounds,[r,n,o,a,h,l]=this.pointer;this.coords[0]+=(r-h)*this.dragSpeed,this.coords[1]+=(n-l)*this.dragSpeed,this.pointer[4]=r,this.pointer[5]=n;const[c,d]=this.coords,[u,p]=this.snapped,m=(1-this.containerFriction)*this.dragSpeed;this.setX(c>e?e+(c-e)*m:c<i?i+(c-i)*m:c,!1),this.setY(d>s?s+(d-s)*m:d<t?t+(d-t)*m:d,!1),this.computeVelocity(r-h,n-l),this.angle=rt(n-a,r-o);const[f,g]=this.snapped;(f!==u&&this.snapX||g!==p&&this.snapY)&&this.onSnap(this)}stop(){this.updateTicker.pause(),this.overshootTicker.pause();for(let t in this.animate.animations)this.animate.animations[t].pause();return ss(this,null,"x"),ss(this,null,"y"),ss(this,null,"progressX"),ss(this,null,"progressY"),ss(this.scroll),ss(this.overshootCoords),this}scrollInView(t,e=0,s=_e.inOutQuad){this.updateScrollCoords();const i=this.destX,r=this.destY,n=this.scroll,o=this.scrollBounds,a=this.canScroll;if(!this.containerArray&&this.isOutOfBounds(o,i,r)){const[h,l,c,u]=o,p=lt(at(r-h,-1e12,0),0),m=lt(at(i-l,0,d),0),f=lt(at(r-c,0,d),0),g=lt(at(i-u,-1e12,0),0);new Pe(n,{x:lt(n.x+(g?g-e:m?m+e:0),0),y:lt(n.y+(p?p-e:f?f+e:0),0),duration:z(t)?350*L.timeScale:t,ease:s,onUpdate:()=>{this.canScroll=!1,this.$scrollContainer.scrollTo(n.x,n.y)}}).init().then((()=>{this.canScroll=a}))}return this}handleHover(){this.isFinePointer&&this.cursor&&!this.cursorStyles&&(this.cursorStyles=ts(this.$trigger,{cursor:this.cursor.onHover}))}animateInView(t,e=0,s=_e.inOutQuad){this.stop(),this.updateBoundingValues();const i=this.x,r=this.y,[n,o,a,h]=this.containerPadding,l=this.scroll.y-this.targetBounds[0]+n+e,c=this.scroll.x-this.targetBounds[1]-o-e,d=this.scroll.y-this.targetBounds[2]-a-e,u=this.scroll.x-this.targetBounds[3]+h+e,p=this.isOutOfBounds([l,c,d,u],i,r);if(p){const[e,n]=this.disabled,o=at(ct(i,this.snapX),u,c),a=at(ct(r,this.snapY),l,d),h=z(t)?350*L.timeScale:t;e||1!==p&&3!==p||this.animate[this.xProp](o,h,s),n||2!==p&&3!==p||this.animate[this.yProp](a,h,s)}return this}handleDown(t){const e=t.target;if(this.grabbed||"range"===e.type)return;t.stopPropagation(),this.grabbed=!0,this.released=!1,this.stop(),this.updateBoundingValues();const i=t.changedTouches,r=i?i[0].clientX:t.clientX,n=i?i[0].clientY:t.clientY,{x:o,y:a}=this.transforms.normalizePoint(r,n),[h,l,c,d]=this.containerBounds,u=(1-this.containerFriction)*this.dragSpeed,p=this.x,m=this.y;this.coords[0]=this.coords[2]=u?p>l?l+(p-l)/u:p<d?d+(p-d)/u:p:p,this.coords[1]=this.coords[3]=u?m>c?c+(m-c)/u:m<h?h+(m-h)/u:m:m,this.pointer[0]=o,this.pointer[1]=a,this.pointer[2]=o,this.pointer[3]=a,this.pointer[4]=o,this.pointer[5]=a,this.pointer[6]=o,this.pointer[7]=a,this.deltaX=0,this.deltaY=0,this.velocity=0,this.velocityStack[0]=0,this.velocityStack[1]=0,this.velocityStack[2]=0,this.velocityStackIndex=0,this.angle=0,this.targetStyles&&(this.targetStyles.revert(),this.targetStyles=null);const f=Ke(this.$target,"zIndex",!1);vs=(f>vs?f:vs)+1,this.targetStyles=ts(this.$target,{zIndex:vs}),this.triggerStyles&&(this.triggerStyles.revert(),this.triggerStyles=null),this.cursorStyles&&(this.cursorStyles.revert(),this.cursorStyles=null),this.isFinePointer&&this.cursor&&(this.bodyStyles=ts(s.body,{cursor:this.cursor.onGrab})),this.scrollInView(100,0,_e.out(3)),this.onGrab(this),s.addEventListener("touchmove",this),s.addEventListener("touchend",this),s.addEventListener("touchcancel",this),s.addEventListener("mousemove",this),s.addEventListener("mouseup",this),s.addEventListener("selectstart",this)}handleMove(t){if(!this.grabbed)return;const e=t.changedTouches,s=e?e[0].clientX:t.clientX,i=e?e[0].clientY:t.clientY,{x:r,y:n}=this.transforms.normalizePoint(s,i),o=r-this.pointer[6],a=n-this.pointer[7];let h=t.target,l=!1,c=!1,d=!1;for(;e&&h&&h!==this.$trigger;){const t=Ke(h,"overflow-y");if("hidden"!==t&&"visible"!==t){const{scrollTop:t,scrollHeight:e,clientHeight:s}=h;if(e>s){d=!0,l=t<=3,c=t>=e-s-3;break}}h=h.parentNode}d&&(!l&&!c||l&&a<0||c&&a>0)?(this.pointer[0]=r,this.pointer[1]=n,this.pointer[2]=r,this.pointer[3]=n,this.pointer[4]=r,this.pointer[5]=n,this.pointer[6]=r,this.pointer[7]=n):(fs(t),this.triggerStyles||(this.triggerStyles=ts(this.$trigger,{pointerEvents:"none"})),this.$trigger.addEventListener("touchstart",fs,{passive:!1}),this.$trigger.addEventListener("touchmove",fs,{passive:!1}),this.$trigger.addEventListener("touchend",fs),(!this.disabled[0]&&J(o)>3||!this.disabled[1]&&J(a)>3)&&(this.updateTicker.resume(),this.pointer[2]=this.pointer[0],this.pointer[3]=this.pointer[1],this.pointer[0]=r,this.pointer[1]=n,this.dragged=!0,this.released=!1,this.onDrag(this)))}handleUp(){if(!this.grabbed)return;this.updateTicker.pause(),this.triggerStyles&&(this.triggerStyles.revert(),this.triggerStyles=null),this.bodyStyles&&(this.bodyStyles.revert(),this.bodyStyles=null);const[t,e]=this.disabled,[r,n,o,a,h,l]=this.pointer,[c,d,u,p]=this.containerBounds,[m,f]=this.snapped,g=this.releaseXSpring,y=this.releaseYSpring,_=this.releaseEase,v=this.hasReleaseSpring,b=this.overshootCoords,x=this.x,T=this.y,w=this.computeVelocity(r-h,n-l),S=this.angle=rt(n-a,r-o),k=150*w,$=(1-this.releaseContainerFriction)*this.dragSpeed,C=x+Z(S)*k,E=T+G(S)*k,B=C>d?d+(C-d)*$:C<p?p+(C-p)*$:C,D=E>u?u+(E-u)*$:E<c?c+(E-c)*$:E,A=this.destX=at(lt(ct(B,this.snapX),5),p,d),N=this.destY=at(lt(ct(D,this.snapY),5),c,u),F=this.isOutOfBounds(this.containerBounds,C,E);let O=0,P=0,R=_,M=_,I=0;if(b.x=x,b.y=T,!t){const t=A===d?x>d?-1:1:x<p?-1:1,s=lt(x-A,0);g.velocity=e&&v?s?k*t/J(s):0:w;const{ease:i,duration:r,restDuration:n}=g;O=x===A?0:v?r:r-n*L.timeScale,v&&(R=i),O>I&&(I=O)}if(!e){const e=N===u?T>u?-1:1:T<c?-1:1,s=lt(T-N,0);y.velocity=t&&v?s?k*e/J(s):0:w;const{ease:i,duration:r,restDuration:n}=y;P=T===N?0:v?r:r-n*L.timeScale,v&&(M=i),P>I&&(I=P)}if(!v&&F&&$&&(O||P)){const t=i.blend;new Pe(b,{x:{to:B,duration:.65*O},y:{to:D,duration:.65*P},ease:_,composition:t}).init(),new Pe(b,{x:{to:A,duration:O},y:{to:N,duration:P},ease:_,composition:t}).init(),this.overshootTicker.stretch(it(O,P)).restart()}else t||this.animate[this.xProp](A,O,R),e||this.animate[this.yProp](N,P,M);this.scrollInView(I,this.scrollThreshold,_);let z=!1;A!==m&&(this.snapped[0]=A,this.snapX&&(z=!0)),N!==f&&this.snapY&&(this.snapped[1]=N,this.snapY&&(z=!0)),z&&this.onSnap(this),this.grabbed=!1,this.dragged=!1,this.updated=!0,this.released=!0,this.onRelease(this),this.$trigger.removeEventListener("touchstart",fs),this.$trigger.removeEventListener("touchmove",fs),this.$trigger.removeEventListener("touchend",fs),s.removeEventListener("touchmove",this),s.removeEventListener("touchend",this),s.removeEventListener("touchcancel",this),s.removeEventListener("mousemove",this),s.removeEventListener("mouseup",this),s.removeEventListener("selectstart",this)}reset(){return this.stop(),this.resizeTicker.pause(),this.grabbed=!1,this.dragged=!1,this.updated=!1,this.released=!1,this.canScroll=!1,this.setX(0,!0),this.setY(0,!0),this.coords[0]=0,this.coords[1]=0,this.pointer[0]=0,this.pointer[1]=0,this.pointer[2]=0,this.pointer[3]=0,this.pointer[4]=0,this.pointer[5]=0,this.pointer[6]=0,this.pointer[7]=0,this.velocity=0,this.velocityStack[0]=0,this.velocityStack[1]=0,this.velocityStack[2]=0,this.velocityStackIndex=0,this.angle=0,this}enable(){return this.enabled||(this.enabled=!0,this.$target.classList.remove("is-disabled"),this.touchActionStyles=ts(this.$trigger,{touchAction:this.disabled[0]?"pan-x":this.disabled[1]?"pan-y":"none"}),this.$trigger.addEventListener("touchstart",this,{passive:!0}),this.$trigger.addEventListener("mousedown",this,{passive:!0}),this.$trigger.addEventListener("mouseenter",this)),this}disable(){return this.enabled=!1,this.grabbed=!1,this.dragged=!1,this.updated=!1,this.released=!1,this.canScroll=!1,this.touchActionStyles.revert(),this.cursorStyles&&(this.cursorStyles.revert(),this.cursorStyles=null),this.triggerStyles&&(this.triggerStyles.revert(),this.triggerStyles=null),this.bodyStyles&&(this.bodyStyles.revert(),this.bodyStyles=null),this.targetStyles&&(this.targetStyles.revert(),this.targetStyles=null),this.$target.classList.add("is-disabled"),this.$trigger.removeEventListener("touchstart",this),this.$trigger.removeEventListener("mousedown",this),this.$trigger.removeEventListener("mouseenter",this),s.removeEventListener("touchmove",this),s.removeEventListener("touchend",this),s.removeEventListener("touchcancel",this),s.removeEventListener("mousemove",this),s.removeEventListener("mouseup",this),s.removeEventListener("selectstart",this),this}revert(){return this.reset(),this.disable(),this.$target.classList.remove("is-disabled"),this.updateTicker.revert(),this.overshootTicker.revert(),this.resizeTicker.revert(),this.animate.revert(),this.resizeObserver.disconnect(),this}handleEvent(t){switch(t.type){case"mousedown":case"touchstart":this.handleDown(t);break;case"mousemove":case"touchmove":this.handleMove(t);break;case"mouseup":case"touchend":case"touchcancel":this.handleUp();break;case"mouseenter":this.handleHover();break;case"selectstart":fs(t)}}}class xs{constructor(t={}){B.current&&B.current.register(this);const i=t.root;let r=s;i&&(r=i.current||i.nativeElement||Nt(i)[0]||s);const n=t.defaults,o=L.defaults,a=t.mediaQueries;if(this.defaults=n?yt(n,o):o,this.root=r,this.constructors=[],this.revertConstructors=[],this.revertibles=[],this.constructorsOnce=[],this.revertConstructorsOnce=[],this.revertiblesOnce=[],this.once=!1,this.onceIndex=0,this.methods={},this.matches={},this.mediaQueryLists={},this.data={},a)for(let t in a){const s=e.matchMedia(a[t]);this.mediaQueryLists[t]=s,s.addEventListener("change",this)}}register(t){(this.once?this.revertiblesOnce:this.revertibles).push(t)}execute(t){let e=B.current,s=B.root,i=L.defaults;B.current=this,B.root=this.root,L.defaults=this.defaults;const r=this.mediaQueryLists;for(let t in r)this.matches[t]=r[t].matches;const n=t(this);return B.current=e,B.root=s,L.defaults=i,n}refresh(){return this.onceIndex=0,this.execute((()=>{let t=this.revertibles.length,e=this.revertConstructors.length;for(;t--;)this.revertibles[t].revert();for(;e--;)this.revertConstructors[e](this);this.revertibles.length=0,this.revertConstructors.length=0,this.constructors.forEach((t=>{const e=t(this);I(e)&&this.revertConstructors.push(e)}))})),this}add(t,e){if(this.once=!1,I(t)){const e=t;this.constructors.push(e),this.execute((()=>{const t=e(this);I(t)&&this.revertConstructors.push(t)}))}else this.methods[t]=(...t)=>this.execute((()=>e(...t)));return this}addOnce(t){if(this.once=!0,I(t)){const e=this.onceIndex++;if(this.constructorsOnce[e])return this;const s=t;this.constructorsOnce[e]=s,this.execute((()=>{const t=s(this);I(t)&&this.revertConstructorsOnce.push(t)}))}return this}keepTime(t){this.once=!0;const e=this.onceIndex++,s=this.constructorsOnce[e];if(I(s))return s(this);const i=xt(t);let r;return this.constructorsOnce[e]=i,this.execute((()=>{r=i(this)})),r}handleEvent(t){"change"===t.type&&this.refresh()}revert(){const t=this.revertibles,e=this.revertConstructors,s=this.revertiblesOnce,i=this.revertConstructorsOnce,r=this.mediaQueryLists;let n=t.length,o=e.length,a=s.length,h=i.length;for(;n--;)t[n].revert();for(;o--;)e[o](this);for(;a--;)s[a].revert();for(;h--;)i[h](this);for(let t in r)r[t].removeEventListener("change",this);t.length=0,e.length=0,this.constructors.length=0,s.length=0,i.length=0,this.constructorsOnce.length=0,this.onceIndex=0,this.matches={},this.methods={},this.mediaQueryLists={},this.data={}}}const Ts=(t,e)=>t&&I(t)?t(e):t,ws=new Map;class Ss{constructor(t){this.element=t,this.useWin=this.element===s.body,this.winWidth=0,this.winHeight=0,this.width=0,this.height=0,this.left=0,this.top=0,this.zIndex=0,this.scrollX=0,this.scrollY=0,this.prevScrollX=0,this.prevScrollY=0,this.scrollWidth=0,this.scrollHeight=0,this.velocity=0,this.backwardX=!1,this.backwardY=!1,this.scrollTicker=new ne({autoplay:!1,onBegin:()=>this.dataTimer.resume(),onUpdate:()=>{const t=this.backwardX||this.backwardY;_t(this,(t=>t.handleScroll()),t)},onComplete:()=>this.dataTimer.pause()}).init(),this.dataTimer=new ne({autoplay:!1,frameRate:30,onUpdate:t=>{const e=t.deltaTime,s=this.prevScrollX,i=this.prevScrollY,r=this.scrollX,n=this.scrollY,o=s-r,a=i-n;this.prevScrollX=r,this.prevScrollY=n,o&&(this.backwardX=s>r),a&&(this.backwardY=i>n),this.velocity=lt(e>0?Math.sqrt(o*o+a*a)/e:0,5)}}).init(),this.resizeTicker=new ne({autoplay:!1,duration:250*L.timeScale,onComplete:()=>{this.updateWindowBounds(),this.refreshScrollObservers(),this.handleScroll()}}).init(),this.wakeTicker=new ne({autoplay:!1,duration:500*L.timeScale,onBegin:()=>{this.scrollTicker.resume()},onComplete:()=>{this.scrollTicker.pause()}}).init(),this._head=null,this._tail=null,this.updateScrollCoords(),this.updateWindowBounds(),this.updateBounds(),this.refreshScrollObservers(),this.handleScroll(),this.resizeObserver=new ResizeObserver((()=>this.resizeTicker.restart())),this.resizeObserver.observe(this.element),(this.useWin?e:this.element).addEventListener("scroll",this,!1)}updateScrollCoords(){const t=this.useWin,s=this.element;this.scrollX=lt(t?e.scrollX:s.scrollLeft,0),this.scrollY=lt(t?e.scrollY:s.scrollTop,0)}updateWindowBounds(){this.winWidth=e.innerWidth,this.winHeight=(()=>{const t=s.createElement("div");s.body.appendChild(t),t.style.height="100lvh";const e=t.offsetHeight;return s.body.removeChild(t),e})()}updateBounds(){const t=getComputedStyle(this.element),e=this.element;let s,i;if(this.scrollWidth=e.scrollWidth+parseFloat(t.marginLeft)+parseFloat(t.marginRight),this.scrollHeight=e.scrollHeight+parseFloat(t.marginTop)+parseFloat(t.marginBottom),this.updateWindowBounds(),this.useWin)s=this.winWidth,i=this.winHeight;else{const t=e.getBoundingClientRect();s=e.clientWidth,i=e.clientHeight,this.top=t.top,this.left=t.left}this.width=s,this.height=i}refreshScrollObservers(){_t(this,(t=>{t._debug&&t.removeDebug()})),this.updateBounds(),_t(this,(t=>{t.refresh(),t._debug&&t.debug()}))}refresh(){this.updateWindowBounds(),this.updateBounds(),this.refreshScrollObservers(),this.handleScroll()}handleScroll(){this.updateScrollCoords(),this.wakeTicker.restart()}handleEvent(t){"scroll"===t.type&&this.handleScroll()}revert(){this.scrollTicker.cancel(),this.dataTimer.cancel(),this.resizeTicker.cancel(),this.wakeTicker.cancel(),this.resizeObserver.disconnect(),(this.useWin?e:this.element).removeEventListener("scroll",this),ws.delete(this.element)}}const ks=(t,e,s,i,r)=>{const n="min"===e,o="max"===e,a="top"===e||"left"===e||"start"===e||n?0:"bottom"===e||"right"===e||"end"===e||o?"100%":"center"===e?"50%":e,{n:h,u:l}=qt(a,jt);let c=h;return"%"===l?c=h/100*s:l&&(c=ke(t,jt,"px",!0).n),o&&i<0&&(c+=i),n&&r>0&&(c+=r),c},$s=(t,e,s,i,r)=>{let n;if(M(e)){const o=C.exec(e);if(o){const a=o[0],h=a[0],l=e.split(a),c="min"===l[0],d="max"===l[0],u=ks(t,l[0],s,i,r),p=ks(t,l[1],s,i,r);if(c){const e=Ut(ks(t,"min",s),p,h);n=e<u?u:e}else if(d){const e=Ut(ks(t,"max",s),p,h);n=e>u?u:e}else n=Ut(u,p,h)}else n=ks(t,e,s,i,r)}else n=e;return lt(n,0)},Cs=t=>{let e;const s=t.targets;for(let t=0,i=s.length;t<i;t++){const i=s[t];if(i[n]){e=i;break}}return e};let Es=0;const Bs=["#FF4B4B","#FF971B","#FFC730","#F9F640","#7AFF5A","#18FF74","#17E09B","#3CFFEC","#05DBE9","#33B3F1","#638CF9","#C563FE","#FF4FCF","#F93F8A"];class Ls{constructor(t={}){B.current&&B.current.register(this);const e=Yt(t.sync,"play pause"),i=e?be(e):null,r=e&&("linear"===e||e===oe),n=e&&!(i===oe&&!r),o=e&&(R(e)||!0===e||r),a=e&&M(e)&&!n&&!o,h=a?e.split(" ").map((t=>()=>{const e=this.linked;return e&&e[t]?e[t]():null})):null,l=a&&h.length>2;this.index=Es++,this.id=z(t.id)?this.index:t.id,this.container=(t=>{const e=t&&Nt(t)[0]||s.body;let i=ws.get(e);return i||(i=new Ss(e),ws.set(e,i)),i})(t.container),this.target=null,this.linked=null,this.repeat=null,this.horizontal=null,this.enter=null,this.leave=null,this.sync=n||o||!!h,this.syncEase=n?i:null,this.syncSmooth=o?!0===e||r?1:e:null,this.onSyncEnter=h&&!l&&h[0]?h[0]:y,this.onSyncLeave=h&&!l&&h[1]?h[1]:y,this.onSyncEnterForward=h&&l&&h[0]?h[0]:y,this.onSyncLeaveForward=h&&l&&h[1]?h[1]:y,this.onSyncEnterBackward=h&&l&&h[2]?h[2]:y,this.onSyncLeaveBackward=h&&l&&h[3]?h[3]:y,this.onEnter=t.onEnter||y,this.onLeave=t.onLeave||y,this.onEnterForward=t.onEnterForward||y,this.onLeaveForward=t.onLeaveForward||y,this.onEnterBackward=t.onEnterBackward||y,this.onLeaveBackward=t.onLeaveBackward||y,this.onUpdate=t.onUpdate||y,this.onSyncComplete=t.onSyncComplete||y,this.reverted=!1,this.completed=!1,this.began=!1,this.isInView=!1,this.forceEnter=!1,this.hasEntered=!1,this.offset=0,this.offsetStart=0,this.offsetEnd=0,this.distance=0,this.prevProgress=0,this.thresholds=["start","end","end","start"],this.coords=[0,0,0,0],this.debugStyles=null,this.$debug=null,this._params=t,this._debug=Yt(t.debug,!1),this._next=null,this._prev=null,bt(this.container,this),Je((()=>{if(!this.reverted){if(!this.target){const e=Nt(t.target)[0];this.target=e||s.body,this.refresh()}this._debug&&this.debug()}}))}link(t){if(t&&(t.pause(),this.linked=t,!this._params.target)){let e;z(t.targets)?_t(t,(t=>{t.targets&&!e&&(e=Cs(t))})):e=Cs(t),this.target=e||s.body,this.refresh()}return this}get velocity(){return this.container.velocity}get backward(){return this.horizontal?this.container.backwardX:this.container.backwardY}get scroll(){return this.horizontal?this.container.scrollX:this.container.scrollY}get progress(){const t=(this.scroll-this.offsetStart)/this.distance;return t===1/0||isNaN(t)?0:lt(at(t,0,1),6)}refresh(){this.reverted=!1;const t=this._params;return this.repeat=Yt(Ts(t.repeat,this),!0),this.horizontal="x"===Yt(Ts(t.axis,this),"y"),this.enter=Yt(Ts(t.enter,this),"end start"),this.leave=Yt(Ts(t.leave,this),"start end"),this.updateBounds(),this.handleScroll(),this}removeDebug(){return this.$debug&&(this.$debug.parentNode.removeChild(this.$debug),this.$debug=null),this.debugStyles&&(this.debugStyles.revert(),this.$debug=null),this}debug(){this.removeDebug();const t=this.container,e=this.horizontal,i=t.element.querySelector(":scope > .animejs-onscroll-debug"),r=s.createElement("div"),n=s.createElement("div"),o=s.createElement("div"),a=Bs[this.index%Bs.length],h=t.useWin,l=h?t.winWidth:t.width,c=h?t.winHeight:t.height,d=t.scrollWidth,u=t.scrollHeight,p=this.container.width>360?320:260,m=e?0:10,f=e?10:0,g=e?24:p/2,y=e?g:15,_=e?60:g,v=e?_:y,b=e?"repeat-x":"repeat-y",x=t=>e?"0px "+t+"px":t+"px 2px",T=t=>`linear-gradient(${e?90:0}deg, ${t} 2px, transparent 1px)`,w=(t,e,s,i,r)=>`position:${t};left:${e}px;top:${s}px;width:${i}px;height:${r}px;`;r.style.cssText=`${w("absolute",m,f,e?d:p,e?p:u)}\n      pointer-events: none;\n      z-index: ${this.container.zIndex++};\n      display: flex;\n      flex-direction: ${e?"column":"row"};\n      filter: drop-shadow(0px 1px 0px rgba(0,0,0,.75));\n    `,n.style.cssText=`${w("sticky",0,0,e?l:g,e?g:c)}`,i||(n.style.cssText+=`background:\n        ${T("#FFFF")}${x(g-10)} / 100px 100px ${b},\n        ${T("#FFF8")}${x(g-10)} / 10px 10px ${b};\n      `),o.style.cssText=`${w("relative",0,0,e?d:g,e?g:u)}`,i||(o.style.cssText+=`background:\n        ${T("#FFFF")}${x(0)} / ${e?"100px 10px":"10px 100px"} ${b},\n        ${T("#FFF8")}${x(0)} / ${e?"10px 0px":"0px 10px"} ${b};\n      `);const S=[" enter: "," leave: "];this.coords.forEach(((t,i)=>{const r=i>1,h=(r?0:this.offset)+t,m=i%2,f=h<v,g=h>(r?e?l:c:e?d:u)-v,b=(r?m&&!f:!m&&!f)||g,x=s.createElement("div"),T=s.createElement("div"),k=e?b?"right":"left":b?"bottom":"top",$=b?(e?_:y)+(r?e?-1:g?0:-2:e?-1:-2):e?1:0;T.innerHTML=`${this.id}${S[m]}${this.thresholds[i]}`,x.style.cssText=`${w("absolute",0,0,_,y)}\n        display: flex;\n        flex-direction: ${e?"column":"row"};\n        justify-content: flex-${r?"start":"end"};\n        align-items: flex-${b?"end":"start"};\n        border-${k}: 2px solid ${a};\n      `,T.style.cssText=`\n        overflow: hidden;\n        max-width: ${p/2-10}px;\n        height: ${y};\n        margin-${e?b?"right":"left":b?"bottom":"top"}: -2px;\n        padding: 1px;\n        font-family: ui-monospace, monospace;\n        font-size: 10px;\n        letter-spacing: -.025em;\n        line-height: 9px;\n        font-weight: 600;\n        text-align: ${e&&b||!e&&!r?"right":"left"};\n        white-space: pre;\n        text-overflow: ellipsis;\n        color: ${m?a:"rgba(0,0,0,.75)"};\n        background-color: ${m?"rgba(0,0,0,.65)":a};\n        border: 2px solid ${m?a:"transparent"};\n        border-${e?b?"top-left":"top-right":b?"top-left":"bottom-left"}-radius: 5px;\n        border-${e?b?"bottom-left":"bottom-right":b?"top-right":"bottom-right"}-radius: 5px;\n      `,x.appendChild(T);let C=h-$+(e?1:0);x.style[e?"left":"top"]=`${C}px`,(r?n:o).appendChild(x)})),r.appendChild(n),r.appendChild(o),t.element.appendChild(r),i||r.classList.add("animejs-onscroll-debug"),this.$debug=r,"static"===Ke(t.element,"position")&&(this.debugStyles=ts(t.element,{position:"relative "}))}updateBounds(){let t;this._debug&&this.removeDebug();const e=this.target,i=this.container,r=this.horizontal,n=this.linked;let o,a=e;for(n&&(o=n.currentTime,n.seek(0,!0)),a.parentElement;a&&a!==i.element&&a!==s.body;){const e="sticky"===Ke(a,"position")&&ts(a,{position:"static"});a=a.parentElement,e&&(t||(t=[]),t.push(e))}const h=e.getBoundingClientRect(),l=r?h.left+i.scrollX-i.left:h.top+i.scrollY-i.top,c=r?h.width:h.height,d=r?i.width:i.height,u=(r?i.scrollWidth:i.scrollHeight)-d,p=this.enter,m=this.leave;let f="start",g="end",y="end",_="start";if(M(p)){const t=p.split(" ");y=t[0],f=t.length>1?t[1]:f}else if(P(p)){const t=p;z(t.container)||(y=t.container),z(t.target)||(f=t.target)}else R(p)&&(y=p);if(M(m)){const t=m.split(" ");_=t[0],g=t.length>1?t[1]:g}else if(P(m)){const t=m;z(t.container)||(_=t.container),z(t.target)||(g=t.target)}else R(m)&&(_=m);const v=$s(e,f,c),b=$s(e,g,c),x=v+l-d,T=b+l-u,w=$s(e,y,d,x,T),S=$s(e,_,d,x,T),k=v+l-w,$=b+l-S,C=$-k;this.offset=l,this.offsetStart=k,this.offsetEnd=$,this.distance=C<=0?0:C,this.thresholds=[f,g,y,_],this.coords=[v,b,w,S],t&&t.forEach((t=>t.revert())),n&&n.seek(o,!0),this._debug&&this.debug()}handleScroll(){const t=this.linked,e=this.sync,s=this.syncEase,i=this.syncSmooth,r=t&&(s||i),n=this.horizontal,o=this.container,a=this.scroll,h=a<=this.offsetStart,l=a>=this.offsetEnd,c=!h&&!l,d=a===this.offsetStart||a===this.offsetEnd,u=!this.hasEntered&&d,p=this._debug&&this.$debug;let m=!1,f=!1,g=this.progress;if(h&&this.began&&(this.began=!1),g>0&&!this.began&&(this.began=!0),r){const e=t.progress;if(i&&R(i)){if(i<1){const t=e<g&&1===g?1e-4:e>g&&!g?-1e-4:0;g=lt(rs(e,g,dt(.01,.2,i),!1)+t,6)}}else s&&(g=s(g));m=g!==this.prevProgress,f=1===e,m&&!f&&i&&e&&o.wakeTicker.restart()}if(p){const t=n?o.scrollY:o.scrollX;p.style[n?"top":"left"]=t+10+"px"}(c&&!this.isInView||u&&!this.forceEnter&&!this.hasEntered)&&(c&&(this.isInView=!0),this.forceEnter&&this.hasEntered?c&&(this.forceEnter=!1):(p&&c&&(p.style.zIndex=""+this.container.zIndex++),this.onSyncEnter(this),this.onEnter(this),this.backward?(this.onSyncEnterBackward(this),this.onEnterBackward(this)):(this.onSyncEnterForward(this),this.onEnterForward(this)),this.hasEntered=!0,u&&(this.forceEnter=!0))),(c||!c&&this.isInView)&&(m=!0),m&&(r&&t.seek(t.duration*g),this.onUpdate(this)),!c&&this.isInView&&(this.isInView=!1,this.onSyncLeave(this),this.onLeave(this),this.backward?(this.onSyncLeaveBackward(this),this.onLeaveBackward(this)):(this.onSyncLeaveForward(this),this.onLeaveForward(this)),e&&!i&&(f=!0)),g>=1&&this.began&&!this.completed&&(e&&f||!e)&&(e&&this.onSyncComplete(this),this.completed=!0,(!this.repeat&&!t||!this.repeat&&t&&t.completed)&&this.revert()),g<1&&this.completed&&(this.completed=!1),this.prevProgress=g}revert(){if(this.reverted)return;const t=this.container;return vt(t,this),t._head||t.revert(),this._debug&&this.removeDebug(),this.reverted=!0,this}}const Ds="undefined"!=typeof Intl&&Intl.Segmenter,As=/\{value\}/g,Ns=/\{i\}/g,Fs=/(\s+)/,Os=/^\s+$/,Ps="line",Rs="word",Ms="char",Is="data-line";let zs=null,Ys=null,Xs=null;const Ws=t=>t.isWordLike||" "===t.segment||R(+t.segment),Vs=t=>t.setAttribute("aria-hidden","true"),Hs=(t,e)=>[...t.querySelectorAll(`[data-${e}]:not([data-${e}] [data-${e}])`)],Us={line:"#00D672",word:"#FF4B4B",char:"#5A87FF"},qs=t=>{if(!t.childElementCount&&!t.textContent.trim()){const e=t.parentElement;t.remove(),e&&qs(e)}},Qs=(t,e,s)=>{const i=t.getAttribute(Is);(null!==i&&+i!==e||"BR"===t.tagName)&&s.add(t);let r=t.childElementCount;for(;r--;)Qs(t.children[r],e,s);return s},js=(t,e={})=>{let s="";const i=M(e.class)?` class="${e.class}"`:"",r=Yt(e.clone,!1),n=Yt(e.wrap,!1),o=n?!0===n?"clip":n:!!r&&"clip";return n&&(s+=`<span${o?` style="overflow:${o};"`:""}>`),s+=`<span${i}${r?' style="position:relative;"':""} data-${t}="{i}">`,r?(s+="<span>{value}</span>",s+=`<span inert style="position:absolute;top:${"top"===r?"-100%":"bottom"===r?"100%":"0"};left:${"left"===r?"-100%":"right"===r?"100%":"0"};white-space:nowrap;">{value}</span>`):s+="{value}",s+="</span>",n&&(s+="</span>"),s},Gs=(t,e,s,i,r,n,o,a,h)=>{const l=r===Ps,c=r===Ms,d=`_${r}_`,u=I(t)?t(s):t,p=l?"block":"inline-block";Xs.innerHTML=u.replace(As,`<i class="${d}"></i>`).replace(Ns,`${c?h:l?o:a}`);const m=Xs.content,f=m.firstElementChild,g=m.querySelector(`[data-${r}]`)||f,y=m.querySelectorAll(`i.${d}`),_=y.length;if(_){f.style.display=p,g.style.display=p,g.setAttribute(Is,`${o}`),l||(g.setAttribute("data-word",`${a}`),c&&g.setAttribute("data-char",`${h}`));let t=_;for(;t--;){const e=y[t],i=e.parentElement;i.style.display=p,l?i.innerHTML=s.innerHTML:i.replaceChild(s.cloneNode(!0),e)}e.push(g),i.appendChild(m)}else console.warn('The expression "{value}" is missing from the provided template.');return n&&(f.style.outline=`1px dotted ${Us[r]}`),f};class Zs{constructor(e,i={}){zs||(zs=Ds?new Ds([],{granularity:Rs}):{segment:t=>{const e=[],s=t.split(Fs);for(let t=0,i=s.length;t<i;t++){const i=s[t];e.push({segment:i,isWordLike:!Os.test(i)})}return e}}),Ys||(Ys=Ds?new Ds([],{granularity:"grapheme"}):{segment:t=>[...t].map((t=>({segment:t})))}),!Xs&&t&&(Xs=s.createElement("template")),B.current&&B.current.register(this);const{words:r,chars:n,lines:o,accessible:a,includeSpaces:h,debug:l}=i,c=(e=O(e)?e[0]:e)&&e.nodeType?e:(At(e)||[])[0],d=!0===o?{}:o,u=!0===r||z(r)?{}:r,p=!0===n?{}:n;this.debug=Yt(l,!1),this.includeSpaces=Yt(h,!1),this.accessible=Yt(a,!0),this.linesOnly=d&&!u&&!p,this.lineTemplate=P(d)?js(Ps,d):d,this.wordTemplate=P(u)||this.linesOnly?js(Rs,u):u,this.charTemplate=P(p)?js(Ms,p):p,this.$target=c,this.html=c&&c.innerHTML,this.lines=[],this.words=[],this.chars=[],this.effects=[],this.effectsCleanups=[],this.cache=null,this.ready=!1,this.width=0,this.resizeTimeout=null;const m=()=>this.html&&(d||u||p)&&this.split();this.resizeObserver=new ResizeObserver((()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout((()=>{const t=c.offsetWidth;t!==this.width&&(this.width=t,m())}),150)})),this.lineTemplate&&!this.ready?s.fonts.ready.then(m):m(),c?this.resizeObserver.observe(c):console.warn("No Text Splitter target found.")}addEffect(t){if(!I(t))return console.warn("Effect must return a function.");const e=xt(t);return this.effects.push(e),this.ready&&(this.effectsCleanups[this.effects.length-1]=e(this)),this}revert(){return clearTimeout(this.resizeTimeout),this.lines.length=this.words.length=this.chars.length=0,this.resizeObserver.disconnect(),this.effectsCleanups.forEach((t=>I(t)?t(this):t.revert&&t.revert())),this.$target.innerHTML=this.html,this}splitNode(t){const e=this.wordTemplate,i=this.charTemplate,r=this.includeSpaces,n=this.debug,o=t.nodeType;if(3===o){const o=t.nodeValue;if(o.trim()){const a=[],h=this.words,l=this.chars,c=zs.segment(o),d=s.createDocumentFragment();let u=null;for(const t of c){const e=t.segment,s=Ws(t);if(!u||s&&u&&Ws(u))a.push(e);else{const t=a.length-1;a[t].includes(" ")||e.includes(" ")?a.push(e):a[t]+=e}u=t}for(let t=0,o=a.length;t<o;t++){const o=a[t];if(o.trim()){const c=a[t+1],u=r&&c&&!c.trim(),p=o,m=i?Ys.segment(p):null,f=i?s.createDocumentFragment():s.createTextNode(u?o+" ":o);if(i){const t=[...m];for(let e=0,r=t.length;e<r;e++){const o=t[e],a=e===r-1&&u?o.segment+" ":o.segment,c=s.createTextNode(a);Gs(i,l,c,f,Ms,n,-1,h.length,l.length)}}e?Gs(e,h,f,d,Rs,n,-1,h.length,l.length):i?d.appendChild(f):d.appendChild(s.createTextNode(o)),u&&t++}else{if(t&&r)continue;d.appendChild(s.createTextNode(o))}}t.parentNode.replaceChild(d,t)}}else if(1===o){const e=[...t.childNodes];for(let t=0,s=e.length;t<s;t++)this.splitNode(e[t])}}split(t=!1){const e=this.$target,i=!!this.cache&&!t,r=this.lineTemplate,n=this.wordTemplate,o=this.charTemplate,a="loading"!==s.fonts.status,h=r&&a;this.ready=!r||a,(h||t)&&this.effectsCleanups.forEach((t=>I(t)&&t(this))),i||(t&&(e.innerHTML=this.html,this.words.length=this.chars.length=0),this.splitNode(e),this.cache=e.innerHTML),h&&(i&&(e.innerHTML=this.cache),this.lines.length=0,n&&(this.words=Hs(e,Rs))),o&&(h||n)&&(this.chars=Hs(e,Ms));const l=this.words.length?this.words:this.chars;let c,d=0;for(let t=0,e=l.length;t<e;t++){const e=l[t],{top:s,height:i}=e.getBoundingClientRect();c&&s-c>.5*i&&d++,e.setAttribute(Is,`${d}`);const r=e.querySelectorAll(`[${Is}]`);let n=r.length;for(;n--;)r[n].setAttribute(Is,`${d}`);c=s}if(h){const t=s.createDocumentFragment(),i=new Set,a=[];for(let t=0;t<d+1;t++){const s=e.cloneNode(!0);Qs(s,t,new Set).forEach((t=>{const e=t.parentElement;e&&i.add(e),t.remove()})),a.push(s)}i.forEach(qs);for(let e=0,s=a.length;e<s;e++)Gs(r,this.lines,a[e],t,Ps,this.debug,e);e.innerHTML="",e.appendChild(t),n&&(this.words=Hs(e,Rs)),o&&(this.chars=Hs(e,Ms))}if(this.linesOnly){const t=this.words;let e=t.length;for(;e--;){const s=t[e];s.replaceWith(s.textContent)}t.length=0}if(this.accessible&&(h||!i)){const t=s.createElement("span");t.style.cssText="position:absolute;overflow:hidden;clip:rect(0 0 0 0);clip-path:inset(50%);width:1px;height:1px;white-space:nowrap;",t.innerHTML=this.html,e.insertBefore(t,e.firstChild),this.lines.forEach(Vs),this.words.forEach(Vs),this.chars.forEach(Vs)}return this.width=e.offsetWidth,(h||t)&&this.effects.forEach(((t,e)=>this.effectsCleanups[e]=t(this))),this}refresh(){this.split(!0)}}const Js={split:(t,e)=>new Zs(t,e)};exports.Animatable=ds,exports.Draggable=bs,exports.JSAnimation=Pe,exports.Scope=xs,exports.ScrollObserver=Ls,exports.Spring=ps,exports.TextSplitter=Zs,exports.Timeline=cs,exports.Timer=ne,exports.WAAPIAnimation=Ge,exports.animate=(t,e)=>new Pe(t,e,null,0,!1).init(),exports.createAnimatable=(t,e)=>new ds(t,e),exports.createDraggable=(t,e)=>new bs(t,e),exports.createScope=t=>new xs(t),exports.createSpring=ms,exports.createTimeline=t=>new cs(t).init(),exports.createTimer=t=>new ne(t,null,0).init(),exports.eases=_e,exports.engine=Bt,exports.onScroll=(t={})=>new Ls(t),exports.scrollContainers=ws,exports.stagger=(t,e={})=>{let s=[],i=0;const r=e.from,n=e.reversed,o=e.ease,a=!z(o),h=a&&!z(o.ease)?o.ease:a?be(o):null,l=e.grid,c=e.axis,d=e.total,u=z(r)||0===r||"first"===r,m="center"===r,f="last"===r,g="random"===r,y=O(t),_=e.use,v=q(y?t[0]:t),b=y?q(t[1]):0,x=S.exec((y?t[1]:t)+p),T=e.start||0+(y?v:0);let w=u?0:R(r)?r:0;return(t,r,o,a)=>{const[u]=Ft(t),p=z(d)?o:d,S=!z(_)&&(I(_)?_(u,r,p):Ht(u,_)),k=R(S)||M(S)&&R(+S)?+S:r;if(m&&(w=(p-1)/2),f&&(w=p-1),!s.length){for(let t=0;t<p;t++){if(l){const e=m?(l[0]-1)/2:w%l[0],i=m?(l[1]-1)/2:et(w/l[0]),r=e-t%l[0],n=i-et(t/l[0]);let o=j(r*r+n*n);"x"===c&&(o=-r),"y"===c&&(o=-n),s.push(o)}else s.push(J(w-t));i=it(...s)}h&&(s=s.map((t=>h(t/i)*i))),n&&(s=s.map((t=>c?t<0?-1*t:-t:J(i-t)))),g&&(s=pt(s))}const $=y?(b-v)/i:v;let C=(a?hs(a,z(e.start)?a.iterationDuration:T):T)+($*lt(s[k],2)||0);return e.modifier&&(C=e.modifier(C)),x&&(C=`${C}${x[2]}`),C}},exports.svg=It,exports.text=Js,exports.utils=as,exports.waapi=Ze;
