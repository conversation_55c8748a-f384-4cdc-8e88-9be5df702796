<!DOCTYPE html>
<html>
<head>
  <title>Interactions tests | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
    #test-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      width: 25rem;
    }
    .square {
      position: relative;
      width: 4.75rem;
      height: 4.75rem;
      margin: .125rem;
      background-color: currentColor;
    }
  </style>
</head>
<body>
  <div id="test-wrapper">
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
    <div class="hover loop square red"></div>
  </div>
  <script type="module">
    import { animate, stagger, utils } from '../../../lib/anime.esm.js';

    // import { inspect } from '../lib/gui/index.js';

    const black = '#252423';
    const white = '#F6F4F2';
    const red = '#FF4B4B';
    const orange = '#FF8F42';
    const lightorange = '#FFC730';
    const yellow = '#F6FF56';
    const citrus = '#A4FF4F';
    const green = '#18FF74';
    const darkgreen = '#00D672';
    const turquoise = '#3CFFEC';
    const skyblue = '#61C3FF';
    const kingblue = '#5A87FF';
    const lavender = '#8453E3';
    const purple = '#C26EFF';
    const pink = '#FB89FB';

    const $hovereds = utils.$('.hover');
    const $loopeds = utils.$('.loop');

    const staggered = animate($loopeds, {
      autoplay: false,
      x: [
        {
          to: ['-50%', 50],
          delay: stagger(120, {grid: [5, 5], from: 14}),
          endDelay: stagger(120, {grid: [5, 5], from: 14, direction: 'reverse'}),
        }, {
          to: '-50%',
          delay: stagger(120, {grid: [5, 5], from: 10}),
          endDelay: stagger(120, {grid: [5, 5], from: 10, direction: 'reverse'}),
        },
      ],
      ease: 'inOutQuart',
      duration: 2000,
      loop: true,
    });

    // // inspect(staggered);

    // animate($loopeds, {
    //   scale: [0, 1],
    //   ease: 'inOutQuad',
    //   duration: 500,
    //   delay: stagger(120, {grid: [5, 5], from: 'center', start: 240}),
    // });

    // createGUI(staggered);

    for (let i = 0, l = $hovereds.length; i < l; i++) {
      const el = $hovereds[i];
      el.onmouseenter = () => {
        staggered.play();
        animate(el, {
          scale: [{ from: 1.4, to: .5, duration: 4000 }, { to: 1.6, duration: 2000 }],
          rotate: [{ to: 45, duration: 2000 }],
          zIndex: { to: 999, modifier: utils.round(0), ease: 'linear', duration: 25 },
          color: { to: el.dataset.clicked ? skyblue : orange, duration: 300, ease: 'out(4)' },
          boxShadow: '0 0 10px 0 rgba(0, 0, 0, .3)',
          duration: 900,
          onBegin() { el.dataset.hover = true; },
        })
      }
      el.onmousedown = () => {
        animate(el, {
          scale: { to: 1, ease: 'outElastic' },
          zIndex: { to: 1, modifier: utils.round(0), ease: 'linear', duration: 25 },
          color: { to: el.dataset.clicked ? red : kingblue, duration: 800 },
          duration: 400,
          onBegin() { el.dataset.clicked ? el.removeAttribute('data-clicked') : el.dataset.clicked = true; },
        })
      }
      el.onmouseleave = () => {
        animate(el, {
          scale: 1,
          rotate: { to: 0, duration: 1200 },
          zIndex: { to: 1, modifier: utils.round(0), ease: 'linear' },
          color: { to: el.dataset.clicked ? kingblue : red },
          boxShadow: { to: '0 0 0 0 rgba(0, 0, 0, 0)', ease: 'linear' },
          duration: 500,
          onBegin() { el.removeAttribute('data-hover'); },
          // complete() { el.style.color = 'green'; },
        })
      }
      el.onmouseup = () => {
        animate(el, {
          scale: el.dataset.clicked ? 1 : (el.dataset.hover ? 1.2 : 1),
          zIndex: { to: el.dataset.hover ? 999 : 1, modifier: utils.round(0), ease: 'linear', duration: 25 },
          color: { to: el.dataset.clicked ? kingblue : (el.dataset.hover ? orange : red), duration: 500 },
          duration: 500
        })
      }
    }

  </script>
</body>
</html>
