<!DOCTYPE html>
<html>
<head>
  <title>Additive fireflies / Anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
      cursor: grab;
      user-select: none;
    }
    body:active {
      cursor: grabbing;
    }
    #animation-wrapper {
      /*font-size: .065vh;*/
      font-size: .2vh;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      overflow: hidden;
      width: 100%;
      height: 100%;
      color: var(--red);
    }
    #circle {
      opacity: .3;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100em;
      height: 100em;
      margin: -50em 0 0 -50em;
      border-radius: 50%;
      will-change: transform, opacity
    }
    #circle:before,
    #circle:after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
    #circle:before {
      z-index: 1;
      opacity: 1;
      box-shadow: inset 0px 0px 25em 0px currentColor,  0px 0px 10em 1px currentColor, 0px 0px 5em 1px currentColor;
    }
    #circle:after {
      z-index: 3;
      opacity: .75;
      box-shadow: inset 0 0 30em 0 currentColor;
      background-image: radial-gradient(circle at 50% 50%, currentColor 0%, transparent 100%);
    }
    .particle {
      opacity: .5;
      position: absolute;
      z-index: 2;
      top: 50%;
      left: 50%;
      width: 5em;
      height: 5em;
      margin: -2.5em 0 0 -2.5em;
      border-radius: 5em;
      will-change: transform;
      mix-blend-mode: plus-lighter;
      background: currentColor;
      box-shadow: 0px 0px 15em 0px currentColor, 0px 0px 5em 0px currentColor;
    }
  </style>
</head>
<body>
  <div id="animation-wrapper">
    <div id="circle"></div>
  </div>
  <script type="module" src="./index.js"></script>
</body>
</html>
