<!DOCTYPE html>
<html>
<head>
  <title>Nested titmlines | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../../assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
    .square {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 4rem;
      height: 4rem;
      margin: 1rem;
      border-radius: .5rem;
      background: var(--red);
    }
  </style>
</head>
<body>
  <div id="animation">
    <div class="square">A</div>
    <div class="square">B</div>
    <div class="square">C</div>
  </div>
  <script type="module" src="./index.js"></script>
</body>
</html>
