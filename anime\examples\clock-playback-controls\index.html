<!DOCTYPE html>
<html>
<head>
  <title>Clock playback controls / anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
      background: #1a1a1a;
      font-family: BerkeleyMono-Regular, monospace;
    }
    #clock {
      font-size: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 40ch;
      height: 20ch;
      padding: 4ch;
      border-radius: 4ch;
/*      background: #202020;*/
    }
    #digital {
      perspective: 1000px;
      display: flex;
      gap: 1ch;
      height: 4ch;
      margin-top: 4ch;
      margin-bottom: 4ch;
    }
    #digital::after {
      content: "";
      display: block;
      position: absolute;
      top: -150%;
      left: -50%;
      width: 200%;
      height: 400%;
      background-image: linear-gradient(180deg, #1a1a1a 10%, #1a1a1a66 30%, #1a1a1a00 40%, #1a1a1a00 60%,#1a1a1a66 70%, #1a1a1a 90%);
    }
    .slot {
      position: relative;
      width: 1ch;
      height: 2ch;
      color: #fff;
      display: flex;
      transform-style: preserve-3d;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 2em;
    }
    .colon {
      transform: translateZ(3ch);
    }
    .numbers > div {
      position: absolute;
      transform-style: preserve-3d;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      backface-visibility: hidden;
      transform-origin: center center 0px;
    }
    #parameters {
      position: fixed;
      right: 0;
      top: 0;
      width: 100%;
      padding: 3ch;
    }
    fieldset {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: .5ch;
      margin-bottom: 1ch;
    }
    fieldset > input {
      min-width: 0;
      border-radius: .1ch;
      accent-color: white;
      color: white;
      text-align: right;
      background: transparent;
    }
    fieldset > label {
      flex-shrink: 0;
      width: 10ch;
    }
    fieldset > .value {
      width: 16ch;
    }
    fieldset > .range {
      width: 100%;
    }
    #controls {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      position: fixed;
      bottom: 0;
      width: 100%;
      padding: 3ch;
    }
    #controls button {
      width: 20%;
      flex-grow: 1;
      flex-shrink: 0;
      height: 4ch;
      min-width: 0;
      border-radius: 1ch;
      margin: .5ch;
      background-color: #2A2A2A;
      color: #FFF;
    }
    #controls button:hover {
      background-color: #3A3A3A;
    }
    #controls button:focus {
      background-color: #FFF;
      color: #2A2A2A;
    }
  </style>
</head>
<body>
  <div id="clock">
    <div id="digital"></div>
  </div>
  <div id="parameters">
    <fieldset id="currentTime">
      <label>currentTime</label>
      <input class="value" type="text" />
      <input class="range" type="range" min="0" max="86400000" step="1" />
    </fieldset>
    <fieldset id="speed">
      <label>speed</label>
      <input class="value" type="text" value="1" />
      <input class="range" type="range" min=".01" max="2" step=".01" value="1" />
    </fieldset>
  </div>
  <div id="controls">
    <button id="play">PLAY</button>
    <button id="pause">PAUSE</button>
    <button id="reverse">REVERSE</button>
    <button id="alternate">ALTERNATE</button>
    <button id="slowmo">SLOW MO</button>
    <button id="speedup">SPEED UP</button>
    <button id="normalspeed">NORMAL SPEED</button>
    <button id="reset">RESET</button>
    <button id="resume">RESUME</button>
    <button id="seek">SEEK</button>
    <button id="complete">COMPLETE</button>
    <button id="restart">RESTART</button>
  </div>
  <script type="module" src="./index.js"></script>
</body>
</html>
