---
name: Feature request
about: Suggest an idea for this project
title: "[FEATURE]"
labels: feature proposal
assignees: ''

---

## Describe what the feature does
A clear and concise description of what the feature request is trying to solve or improve.
If you're using an LLM to write the issue, please make sure that the text actually means something, and please, keep it short, LLMs tend to write longer texts than needed.

## Provide a code example of what the feature should look like
A clear and concise description of what you want to happen, with an example of how the API should look like.
Do not post screenshots of your text editor, paste the code directly in the issue with the adequate code formatting.

## Describe alternatives you've considered
A clear and concise description of any alternative solutions or features you've considered.

## Additional context
Add any other context or screen recording about the feature request here.
