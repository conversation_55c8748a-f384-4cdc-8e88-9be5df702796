<!DOCTYPE html>
<html>
<head>
  <title>Anime logo animation V3 / Anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 100%;
      height: 100vh;
    }

    /* Logo */

    .main-logo {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 600px;
    }

    .logo-animation-wrapper {
      position: relative;
      width: 100%;
      padding-bottom: 12%;

    }

    .logo-animation {
      pointer-events: none;
      overflow: visible;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1000px;
      height: 240px;
      margin: -120px 0 0 -500px;
      /*transform: scale(.633333);*/
    }

    .anime-logo {
      overflow: visible;
      position: relative;
      display: flex;
      flex-direction: column;
      width: 1000px;
      height: 120px;
    }

    .anime-logo-signs {
      overflow: visible;
      display: flex;
      align-items: flex-end;
      position: relative;
      width: 100%;
      height: 512px;
      margin-top: -352px;
    }

    .logo-letter {
      display: flex;
      align-items: flex-end;
      overflow: hidden;
      height: 100%;
    }

    .bounced {
      transform-origin: 50% 100% 0px;
      transform: translateY(200px) scaleX(.55) scaleY(.8);
    }

    .logo-animation .bounce {
      overflow: visible;
      position: absolute;
      left: 0;
      bottom: 70px;
    }

/*    .logo-animation .bounce svg {
      box-shadow: inset 0 0 0 1px red;
    }

    .logo-animation .bounce path {
      stroke: red;
    }*/

    .logo-animation .dot {
      opacity: 0.001;
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 40px;
      height: 40px;
      margin: -20px 0 0 -20px;
      background-color: currentColor;
      transform: translate3d(0,0,0);
    }

    .logo-animation .logo-letter svg {
      overflow: visible;
      fill: none;
      fill-rule: evenodd;
    }

    .logo-animation .line {
      fill: none;
      fill-rule: evenodd;
      stroke-linecap: square;
      stroke-width: 40;
      stroke: currentColor;
    }

    .logo-animation .fill {
      opacity: .001;
      stroke: currentColor;
      stroke-width: 40px;
    }

    .logo-text {
      opacity: .001;
      margin-top: .25em;
      font-weight: 400;
      font-size: 11px;
      line-height: 1;
      letter-spacing: .125em;
      text-align: justify;
      word-break: keep-all;
    }

    .logo-text:after {
      content: "";
      display: inline-block;
      width: 100%;
      height: 0;
      font-size: 0;
      line-height: 0;
    }
  </style>
</head>
<body>
<div class="main-logo">
  <div class="logo-animation-wrapper">
    <div class="logo-animation">
      <div class="anime-logo">
        <div class="anime-logo-signs">
          <div class="logo-letter letter-a">
            <svg class="bounced" viewBox="0 0 200 240" width="200" height="240">
              <path class="line" d="M30 20h130c9.996 0 10 40 10 60v140H41c-11.004 0-11-40-11-60s-.004-60 10-60h110"/>
            </svg>
          </div>
          <div class="logo-letter letter-n">
            <svg class="bounced" viewBox="0 0 200 240" width="200" height="240">
              <path class="line" d="M170 220V60c0-31.046-8.656-40-19.333-40H49.333C38.656 20 30 28.954 30 60v160"/>
            </svg>
          </div>
          <div class="logo-letter letter-i">
            <svg class="bounced" viewBox="0 0 60 240" width="60" height="240">
              <path class="line"
                      d="M30 20v200"
                data-d2="M30 100v120"
              />
            </svg>
          </div>
          <div class="logo-letter letter-m">
            <svg class="bounced" viewBox="0 0 340 240" width="340" height="240" fill="none" fill-rule="evenodd">
              <path class="line"
                      d="M240,220 L240,60 C240,28.954305 231.344172,20 220.666667,20 C171.555556,20 254.832031,20 170,20 C85.1679688,20 168.444444,20 119.333333,20 C108.655828,20 100,28.954305 100,60 L100,220"
                data-d2="M310,220 L310,60 C310,28.954305 301.344172,20 290.666667,20 C241.555556,20 254.832031,110 170,110 C85.1679688,110 98.4444444,20 49.3333333,20 C38.6558282,20 30,28.954305 30,60 L30,220"
                data-d3="M310,220 L310,60 C310,28.954305 301.344172,20 290.666667,20 C241.555556,20 254.832031,20 170,20 C85.1679688,20 98.4444444,20 49.3333333,20 C38.6558282,20 30,28.954305 30,60 L30,220"
              />
            </svg>
          </div>
          <div class="logo-letter letter-e">
            <svg class="bounced" viewBox="0 0 200 240" width="200" height="240">
              <path class="line" d="M50 140h110c10 0 10-40 10-60s0-60-10-60H40c-10 0-10 40-10 60v80c0 20 0 60 10 60h130"/>
            </svg>
          </div>
          <div class="bounce">
            <svg viewBox="0 0 1000 260" width="1000" height="260" fill="none">
              <path d="M630,240 C630,111.154418 608.971354,40 530.160048,40 C451.348741,40 430,127.460266 430,210"/>
            </svg>
            <div class="dot"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
  <script type="module">

    import { createTimeline, utils, stagger, svg, createSpring } from '../../../lib/anime.esm.js';
    import { inspect } from '../../lib/gui/index.js';

   function fitElementToParent(el, padding, exception) {
    var timeout = null;
    function resize() {
      if (timeout) clearTimeout(timeout);
      utils.set(el, {scale: 1});
      if (exception) utils.set(exception, {scale: 1});
      var pad = padding || 0;
      var parentEl = el.parentNode;
      var elOffsetWidth = el.offsetWidth - pad;
      var parentOffsetWidth = parentEl.offsetWidth;
      var ratio = parentOffsetWidth / elOffsetWidth;
      var invertedRatio = elOffsetWidth / parentOffsetWidth;
      timeout = setTimeout(function() {
        utils.set(el, {scale: ratio});
        if (exception) utils.set(exception, {scale: invertedRatio});
      }, 10);
    }
    resize();
    window.addEventListener('resize', resize);
  }

  // main logo animation

  var logoAnimation = (function() {

    var logoAnimationEl = document.querySelector('.logo-animation');
    var bouncePath = svg.createMotionPath('.bounce path');

    fitElementToParent(logoAnimationEl, 0, '.bounce svg');

    utils.set(['.letter-a', '.letter-n', '.letter-i'], {translateX: 70});
    utils.set('.letter-e', {translateX: -70});
    utils.set('.dot', { translateX: 630, translateY: -200 });

    var logoAnimationTL = createTimeline({
      autoplay: false,
      defaults: {
        ease: 'outSine'
      }
    })
    .add('.letter-i .line', {
      duration: 0,
      onBegin: function(a) { a.targets[0].removeAttribute('stroke-dasharray'); }
    }, 0)
    .add('.bounced', {
      transformOrigin: ['50% 100% 0px', '50% 100% 0px'],
      translateY: [
        {to: [150, -160], duration: 190, endDelay: 20, ease: 'cubicBezier(0.225, 1, 0.915, 0.980)'},
        {to: 4, duration: 120, ease: 'inQuad'},
        {to: 0, duration: 120, ease: 'outQuad'}
      ],
      scaleX: [
        {to: [.25, .85], duration: 190, ease: 'outQuad'},
        {to: 1.08, duration: 120, delay: 85, ease: 'inOutSine'},
        {to: 1, duration: 260, delay: 25, ease: 'outQuad'}
      ],
      scaleY: [
        {to: [.3, .8], duration: 120, ease: 'outSine'},
        {to: .35, duration: 120, delay: 180, ease: 'inOutSine'},
        {to: .57, duration: 180, delay: 25, ease: 'outQuad'},
        {to: .5, duration: 190, delay: 15, ease: 'outQuad'}
      ],
      delay: stagger(80)
    }, 0)
    .add('.dot', {
      opacity: { to: 1, duration: 100 },
      translateY: 250,
      scaleY: [4, .7],
      scaleX: { to: 1.3, delay: 100, duration: 200},
      duration: 280,
      ease: 'cubicBezier(0.350, 0.560, 0.305, 1)'
    }, '-=290')
    .add('.letter-m .line', {
      ease: 'outElastic(1, .8)',
      duration: 600,
      d: el => el.dataset.d2,
      onBegin: function(a) { a.targets[0].removeAttribute('stroke-dasharray'); }
    }, '-=140')
    .add(['.letter-a', '.letter-n', '.letter-i', '.letter-e'], {
      translateX: 0,
      ease: 'outElastic(1, .6)',
      duration: 800,
      delay: stagger(40, {from: 2.5}),
      onRender: function(a) { a.targets[2].removeAttribute('stroke-dasharray'); }
    }, '-=600')
    .add('.letter-m .line', {
      d: el => el.dataset.d3,
      ease: createSpring({velocity:10}),
    }, '-=680')
    .add('.dot', {
      translateX: bouncePath.translateX,
      translateY: bouncePath.translateY,
      rotate: { to: '1turn', duration: 790 },
      scaleX: { to: 1, duration: 50, ease: 'outSine' },
      scaleY: [
        { to: [1, 1.5], duration: 50, ease: 'inSine' },
        { to: 1, duration: 50, ease: 'outExpo' }
      ],
      ease: 'cubicBezier(0, .74, 1, .255)',
      duration: 800
    }, '<<')
    .add('.dot', {
      scale: 1,
      rotate: '1turn',
      scaleY: {to: .5, delay: 0, duration: 150, delay: 230},
      translateX: 430,
      translateY: [
        {to: 244, duration: 100},
        {to: 204, duration: 200, delay: 130},
        {to: 224, duration: 225, ease: 'outQuad', delay: 25}
      ],
      duration: 200,
      ease: 'outSine'
    }, '<')
    .add('.letter-i .line', {
      transformOrigin: ['50% 100% 0', '50% 100% 0'],
      d: el => el.dataset.d2,
      ease: 'cubicBezier(0.400, 0.530, 0.070, 1)',
      duration: 80
    }, '<<')
    .add('.logo-letter', {
      translateY: [
        {to: 40, duration: 150, ease: 'outQuart'},
        {to: 0, duration: 800, ease: 'outElastic(1, .5)'}
      ],
      // strokeDashoffset: svg.drawLine(),
      delay: stagger(60, {from: 'center'})
    }, '<<')
    .add('.bounced', {
      scaleY: [
        {to: .4, duration: 150, ease: 'outQuart'},
        {to: .5, duration: 800, ease: 'outElastic(1, .5)'}
      ],
      delay: stagger(60, {from: 'center'})
    }, '<-=1090')
    .init();

    return logoAnimationTL;

  })();


  logoAnimation.play();

  inspect(logoAnimation);

  </script>
</body>
</html>
