<!DOCTYPE html>
<html>
<head>
  <title>Text / Split playground / Anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="../../assets/css/styles.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap" rel="stylesheet">
  <style>
    body {
      opacity: 0;
      display: flex;
      flex-direction: row;
      gap: 2rem;
      width: 100%;
      min-height: 100dvh;
      padding: 2rem;
    }

    body.is-ready {
      opacity: 1;
    }

    aside {
      --border-radius: .75rem;
      justify-self: flex-end;
      width: 12rem;
      border-radius: var(--border-radius);
    }

    aside li {
      list-style: none;
      padding: 1rem;
      background-color: #404040;
      margin-bottom: 1px;
    }

    aside li:first-child {
      border-top-left-radius: var(--border-radius);
      border-top-right-radius: var(--border-radius);
    }

    aside li:last-child {
      border-bottom-left-radius: var(--border-radius);
      border-bottom-right-radius: var(--border-radius);
    }

    .field input,
    .field select {
      width: 50%;
    }

    .field {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    fieldset:disabled .field {
      opacity: .5;
    }

    main {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 2rem;
      width: calc(100% - 12rem);
    }

    article {
      width: 100%;
      max-width: 800px;
      justify-self: center;
      font-family: "Noto Sans JP", sans-serif;
      font-weight: 400;
      font-kerning: none;
      font-variant-ligatures: none;
      text-rendering: optimizeSpeed;
    }

    article h1 {
      font-size: 3em;
      font-weight: 600;
      margin-bottom: .5em;
      line-height: 1.4;
    }

    article p {
      font-size: 1.25rem;
      line-height: 1.5;
      margin-bottom: 1rem;
    }

    article ul {
      padding-left: 1rem;
      margin-bottom: 1rem;
    }

    article a {
      color: var(--red);
      text-decoration: underline;
    }

    article a span {
      text-decoration: underline;
    }

    .nested { color: rgba(200, 100, 100, 1) }
    .nested .nested { color: rgba(200, 100, 100, .8) }
    .nested .nested .nested { color: rgba(200, 100, 100, .6) }
    .nested .nested .nested .nested { color: rgba(200, 100, 100, .4) }
    .nested .nested .nested .nested .nested { color: rgba(200, 100, 100, .2) }

    code {
      color: rgba(200, 200, 200, 1);
      font-family: monospace;
    }

    article span:has(~ span h1) h1,
    article span:has(~ span p) p,
    article span:has(~ span ul) ul {
      margin-bottom: 0;
    }

    article span:has(p) + span:has(ul) {
      margin-top: 1rem;
    }

  </style>
</head>
<body class="is-debug">
  <main>
    <article>
      <h1>An all-in-one text splitter function to easily split, wrap, and clone text<br></h1>
      <p>
        Anime.js <code>text.split()</code> utility function is an easy to use, fully-featured, lightweight and performant modern text splitter for JavaScript.<br>
        It does all the following in only <strong>7KB</strong>:
      </p>
      <ul>
        <li>Can split text by characters, words, and lines.</li>
        <li>Support splitting through elements like <a href="#">links with <strong>nested elements like</strong></a> <code>&lt;strong&gt;</code> tags and <span class="nested">any <span class="nested">level <span class="nested">of <span class="nested">nested <span class="nested">tags</span></span></span></span></span> automatically.</li>
        <li>Element cloning with configurable position.</li>
        <li>Use your own custom wrapper with a powerful templating system.</li>
        <li>Split words in languages that don't use spaces like Chinese and Japanese この日本語の文のように.</li>
        <li>Handles splitting ѕρє¢ιαℓ ¢нαяα¢тєяѕ and 🇪Ⓜ️🅾️🇯ℹ️s.</li>
        <li>Is accessible for screen readers.</li>
        <li>And is 100% responsive.</li>
      </ul>
      <p>
        Check out the full documenation and learn more at <a href="https://animejs.com/documentation/text/split">animejs.com/documentation</a>.
      </p>
    </article>
  </main>
  <aside id="parameters">
    <form id="splitForm">
      <ul>
        <li>
          <fieldset class="vertical" id="lines-options">
            <legend>
              <label for="lines"><input type="checkbox" id="lines" name="lines"> lines</label>
            </legend>
            <div class="field">
              <label for="lines-wrap">wrap</label>
              <select id="lines-wrap" name="lines-wrap">
                <option value="false">false</option>
                <option value="clip">clip</option>
                <option value="visible">visible</option>
              </select>
            </div>
            <div class="field">
              <label for="lines-clone">clone</label>
              <select id="lines-clone" name="lines-clone">
                <option value="false">false</option>
                <option value="top">top</option>
                <option value="right">right</option>
                <option value="bottom">bottom</option>
                <option value="left">left</option>
              </select>
            </div>
            <div class="field">
              <label for="lines-animate">animate</label>
              <select id="lines-animate" name="lines-animate">
                <option value="false">false</option>
                <option value="250">200ms</option>
                <option value="500">500ms</option>
                <option value="750">750ms</option>
                <option value="1000">1000ms</option>
              </select>
            </div>
            <div class="field">
              <label for="lines-stagger">stagger</label>
              <select id="lines-stagger" name="lines-stagger">
                <option value="10">10</option>
                <option value="50">50</option>
                <option value="200">200</option>
                <option value="random">random</option>
              </select>
            </div>
          </fieldset>
        </li>
        <li>
          <fieldset class="vertical" id="words-options">
            <legend>
              <label for="words"><input type="checkbox" id="words" name="words" checked> words</label>
            </legend>
            <div class="field">
              <label for="words-wrap">wrap</label>
              <select id="words-wrap" name="words-wrap">
                <option value="false">false</option>
                <option value="clip">clip</option>
                <option value="visible">visible</option>
              </select>
            </div>
            <div class="field">
              <label for="words-clone">clone</label>
              <select id="words-clone" name="words-clone">
                <option value="false">false</option>
                <option value="top">top</option>
                <option value="right">right</option>
                <option value="bottom">bottom</option>
                <option value="left">left</option>
              </select>
            </div>
            <div class="field">
              <label for="words-animate">animate</label>
              <select id="words-animate" name="words-animate">
                <option value="false">false</option>
                <option value="250">250ms</option>
                <option value="500">500ms</option>
                <option value="750">750ms</option>
                <option value="1000">1000ms</option>
              </select>
            </div>
            <div class="field">
              <label for="words-stagger">stagger</label>
              <select id="words-stagger" name="words-stagger">
                <option value="10">10</option>
                <option value="50">50</option>
                <option value="200">200</option>
                <option value="random">random</option>
              </select>
            </div>
          </fieldset>
        </li>
        <li>
          <fieldset class="vertical" id="chars-options">
            <legend>
              <label for="chars"><input type="checkbox" id="chars" name="chars"> chars</label>
            </legend>
            <div class="field">
              <label for="chars-wrap">wrap</label>
              <select id="chars-wrap" name="chars-wrap">
                <option value="false">false</option>
                <option value="clip">clip</option>
                <option value="visible">visible</option>
              </select>
            </div>
            <div class="field">
              <label for="chars-clone">clone</label>
              <select id="chars-clone" name="chars-clone">
                <option value="false">false</option>
                <option value="top">top</option>
                <option value="right">right</option>
                <option value="bottom">bottom</option>
                <option value="left">left</option>
              </select>
            </div>
            <div class="field">
              <label for="chars-animate">animate</label>
              <select id="chars-animate" name="chars-animate">
                <option value="false">false</option>
                <option value="250">250ms</option>
                <option value="500">500ms</option>
                <option value="750">750ms</option>
                <option value="1000">1000ms</option>
              </select>
            </div>
            <div class="field">
              <label for="chars-stagger">stagger</label>
              <select id="chars-stagger" name="chars-stagger">
                <option value="10">10</option>
                <option value="50">50</option>
                <option value="200">200</option>
                <option value="random">random</option>
              </select>
            </div>
          </fieldset>
        </li>
        <li>
          <label for="includeSpaces"><input type="checkbox" id="includeSpaces" name="includeSpaces"> includeSpaces</label>
          <label for="accessible"><input type="checkbox" id="accessible" name="accessible" checked> accessible</label>
          <label for="debug"><input type="checkbox" id="debug" name="debug" checked> debug</label>
        </li>
        <li>
          <fieldset>
            <button type="button" class="button" id="revert" name="revert">Revert</button>
            <button type="button" class="button" id="split" name="revert">Split</button>
          </fieldset>
        </li>
      </ul>
    </form>
  </aside>
  <script type="module" src="./index.js"></script>
</body>
</html>