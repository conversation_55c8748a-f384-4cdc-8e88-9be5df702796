<!DOCTYPE html>
<html>
<head>
  <title>Draggable infinite auto carousel / Anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <link href="../assets/css/styles.css" rel="stylesheet">
  <style>
    * {
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      user-select: none;
    }
    body, html {
      overflow: hidden;
      background: #000;
    }
    #infinite-carousel {
      --spacing: 30px;
      display: hidden;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    .carousel {
      display: flex;
      height: 100%;
      padding: var(--spacing) 0;
      will-change: transform;
    }
    .carousel-item {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 25vw;
      flex-shrink: 0;
      width: 100%;
      height: 100%;
      margin-right: var(--spacing);
      background-color: var(--red);
      border-radius: var(--spacing);
      list-style: none;
    }
  </style>
</head>
<body>
  <div id="infinite-carousel">
    <ul class="carousel">
      <li class="draggable carousel-item" style="width: 35vw">1</li>
      <li class="draggable carousel-item" style="width: 60vw">2</li>
      <li class="draggable carousel-item" style="width: 85vw">3</li>
      <li class="draggable carousel-item" style="width: 60vw">4</li>
    </ul>
  </div>
  <script src="./index.js" type="module"></script>
</body>

</html>
