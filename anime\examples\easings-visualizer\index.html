<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Easings visualizer</title>
  <link href="../assets/css/styles.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 100dvw;
      min-height: 100dvh;
    }

    polyline {
      vector-effect: non-scaling-stroke;
    }

    input[type=number] {
      -moz-appearance: textfield;
      appearance: textfield;
      margin: 0;
    }

    input[type=number]::-webkit-inner-spin-button,
    input[type=number]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    #eases-visualizer {
      --unit: 1dvw;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100dvw;
      min-height: 100dvh;
    }

    @media (min-width: 640px) {
      #eases-visualizer {
        --unit: .7125dvw;
      }
    }

    #eases-editor {
      position: fixed;
      right: 0;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 70vw;
    }

    @media (min-width: 640px) {
      #eases-editor {
        width: 50%;
        height: 100%;
      }
    }

    #eases-container {
      --one-cell: calc(var(--unit) * 10);
      position: relative;
      width: calc(var(--one-cell) * 5);
      height: calc(var(--one-cell) * 5);
      display: grid;
      grid-template-columns: 3fr 1fr 1fr;
      grid-template-rows: 1fr 1fr 3fr;
      gap: 0px 0px;
      grid-auto-flow: row;
      grid-template-areas:
        "time time ."
        "graph graph preview"
        "graph graph preview";
    }

    .eases-editor-cell {
      position: relative;
    }

    #eases-graph {
      grid-area: graph;
      border: 1px dotted rgba(255, 255, 255, .5);
    }

    #eases-container:before {
      content: "";
      position: absolute;
      left: -100%;
      top: -100%;
      display: block;
      width: 300%;
      height: 300%;
      background:
        linear-gradient(-90deg, rgba(255,255,255,.05) 1px, transparent 1px),
        linear-gradient(rgba(255,255,255,.05) 1px, transparent 1px),
        linear-gradient(-90deg, rgba(255,255,255,.04) 1px, transparent 1px),
        linear-gradient(rgba(255,255,255,.04) 1px, transparent 1px),
        linear-gradient(transparent 3px, var(--black) 3px, var(--black) calc(var(--one-cell) - 2px), transparent calc(var(--one-cell) - 2px)),
        linear-gradient(-90deg, var(--white) 1px, transparent 1px),
        linear-gradient(-90deg, transparent 3px, var(--black) 3px, var(--black) calc(var(--one-cell) - 2px), transparent calc(var(--one-cell) - 2px)),
        linear-gradient(var(--white) 1px, transparent 1px), var(--black);
      background-size:
        calc(var(--one-cell) / 10) calc(var(--one-cell) / 10),
        calc(var(--one-cell) / 10) calc(var(--one-cell) / 10),
        var(--one-cell) var(--one-cell),
        var(--one-cell) var(--one-cell),
        var(--one-cell) var(--one-cell),
        var(--one-cell) var(--one-cell),
        var(--one-cell) var(--one-cell),
        var(--one-cell) var(--one-cell);
    }

    #eases-graph svg {
      overflow: visible;
      position: absolute;
      width: 100%;
      height: 100%;
      stroke-width: 1px;
      will-change: transform;
    }

    #eases-time {
      grid-area: time;
      border-top: 1px dashed rgba(255, 255, 255, .5);
    }

    #eases-preview {
      grid-area: preview;
      border-right: 1px dashed rgba(255, 255, 255, .5);
    }

    .axis {
      z-index: 1;
      position: absolute;
    }

    .axis:after {
      content: "";
      display: block;
      position: absolute;
      top: -1.25rem;
      right: -1.25rem;
      width: calc(2.5rem + 1px);
      height: calc(2.5rem + 1px);
    }

    .axis-x {
      bottom: -1px;
      right: -1px;
      width: calc(var(--unit) * 50);
      height: 1px;
      background-color: var(--red);
      z-index: 1;
    }

    .axis-x:not(:first-child) {
      z-index: 0;
    }

    .axis-x:first-child:after {
      background-color: var(--red);
      box-shadow: 0 0 0 1px var(--black);
    }

    .axis-x:not(:first-child):after {
      background-color: var(--red);
    }

    .axis-y {
      top: 0;
      left: -1px;
      width: 1px;
      height: calc(var(--unit) * 50);
      background-color: rgba(255, 255, 255, .25);
    }

    .axis-y:after {
      content: attr(data-value);
      top: -1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: .75rem;
      font-weight: bold;
      color: var(--white);
      background-color: var(--black);
      border: 1px solid rgba(255, 255, 255, .25);
    }

    /* Easings config */

    #eases-config {
      position: sticky;
      z-index: 2;
      top: 0;
      width: 100%;
      min-height: 100dvh;
      margin-top: 70vw;
      background: var(--black);
    }

    @media (min-width: 640px) {
      #eases-config {
        width: 50%;
        margin-top: 0;
      }
    }

    #ease-controls {
      position: sticky;
      z-index: 2;
      top: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
      padding: .75rem;
      padding-bottom: 0;
      background: var(--black);
      box-shadow: 0 10px 10px 0 var(--black);
      border-radius: 1rem 1rem 0 0;
    }

    #ease-name {
      background: transparent;
      color: var(--red);
      width: 100%;
      margin: 0.125rem;
      font-size: 2rem;
    }

    @media (min-width: 1280px) {
      #ease-controls {
        padding: 1.5rem;
        padding-bottom: 0;
      }

      #ease-name {
        margin: 0.5rem;
      }
    }

    #ease-parameters {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
    }

    .ease-parameter {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: calc(25% - .25rem);
      margin: .125rem;
    }

    .parameter-legend {
      display: block;
      height: 1rem;
      font-size: .75rem;
      margin-bottom: .5rem;
    }

    .ease-parameter input[type="number"] {
      display: none;
    }

    .ease-parameter input[type="range"] {
      width: 100%;
    }

    @media (min-width: 1280px) {
      .ease-parameter input[type="number"] {
        display: block;
        width: 2.75rem;
        background-color: transparent;
        color: var(--white);
      }
      .ease-parameter input[type="range"] {
        width: calc(100% - 2.75rem);
      }
    }

    .ease-parameter input:disabled {
      opacity: .25;
    }

    input[type="range"][value] {
      position: relative;
      height: 2rem;
      -webkit-appearance: none;
      background-color: transparent;
      color: currentColor;
    }

    input[type="range"][value]:focus {
      outline: none;
    }

    input[type="range"][value]:after {
      content: "";
      position: absolute;
      top: 1rem;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: currentColor;
    }

    input[type="range"][value]::-webkit-slider-thumb {
      position: relative;
      z-index: 2;
      -webkit-appearance: none;
      border-radius: 4px;
      border: 1px solid currentColor;
      width: 16px;
      height: 29px;
      background-color: var(--black);
      background-image: radial-gradient(transparent 1px, rgba(0, 0, 0, .7) 0), radial-gradient(transparent 1px, currentColor 0), radial-gradient(currentColor 1px, transparent 0);
      background-size: 3px 3px, 3px 3px, 3px 3px;
      background-position: -2px -2px, -2px -2px, -2px -2px;
      box-shadow: inset 0 0 0 4.5px rgba(0, 0, 0, .7), inset 0 0 0 4.5px currentColor;
    }

    /* Ease buttons */

    #eases-list {
      position: relative;
      z-index: 1;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
      min-height: 100%;
      padding: .75rem;
      background-color: var(--black);
    }

    @media (min-width: 1280px) {
      #eases-list {
        padding: 1.5rem;
      }
    }

    button.ease-button {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: calc(25% - .25rem);
      height: auto;
      margin: .125rem;
      padding: .5rem;
      background-color: rgba(255, 255, 255, .05);
      color: var(--white);
      cursor: pointer;
      border-radius: .6rem;
      transition: background-color .25s ease-in-out;
      font-size: .75rem;
    }

    @media (min-width: 1280px) {
      button.ease-button {
        width: calc(12.5% - .25rem);
      }
    }

    button.ease-button:hover {
      background-color: rgba(255, 255, 255, .1);
      transition: background-color .05s ease-out;
    }

    button.ease-button.is-active {
      background: var(--red);
      color: var(--black);
    }

    button.ease-button .ease-config {
      position: absolute;
      top: .25rem;
      right: .41rem;
      font-size: .75rem;
    }

    button.ease-button svg {
      overflow: visible;
      width: calc(100% - 3rem);
      margin-top: 0.25rem;
      margin-bottom: 0.5rem;
    }

    @media (min-width: 640px) {
      button.ease-button svg {
        width: calc(100% - 2rem);
        margin-top: 1rem;
        margin-bottom: 1rem;
      }
    }

    button.ease-button svg polyline {
      stroke: var(--red);
    }

    button.ease-button.is-active svg polyline {
      stroke: var(--black);
    }

  </style>
</head>
<body>
  <div id="eases-visualizer">
    <div id="eases-editor">
      <div id="eases-container">
        <div id="eases-graph" class="eases-editor-cell">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" class="grid-small">
            <polyline id="ease-curve" stroke="#FF4B4B" stroke-width="4" points="0 100 50 50 100 0" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd"/>
          </svg>
        </div>
        <div id="eases-time" class="eases-editor-cell"><div class="axis axis-y" data-value="1.0"></div></div>
        <div id="eases-preview" class="eases-editor-cell">
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
          <div class="axis axis-x"></div>
        </div>
      </div>
    </div>
    <div id="eases-config">
      <div id="ease-controls" class="ease-visualizer-section">
        <input type="text" id="ease-name" value="easeName" readonly/>
        <div id="ease-parameters">
          <fieldset class="ease-parameter">
            <legend id="legend-p1" class="parameter-legend">P1</legend>
            <input data-parameters="p1" type="number" value="1.0" min="0" max="2" step="0.1"/>
            <input data-parameters="p1" type="range" value="1.0" min="0" max="2" step="0.1"/>
          </fieldset>
          <fieldset class="ease-parameter">
            <legend id="legend-p2" class="parameter-legend">P2</legend>
            <input data-parameters="p2" type="number" value="1.0" min="0" max="2" step="0.1"/>
            <input data-parameters="p2" type="range" value="1.0" min="0" max="2" step="0.1"/>
          </fieldset>
          <fieldset class="ease-parameter">
            <legend id="legend-p3" class="parameter-legend">P3</legend>
            <input data-parameters="p3" type="number" value="1.0" min="0" max="2" step="0.1"/>
            <input data-parameters="p3" type="range" value="1.0" min="0" max="2" step="0.1"/>
          </fieldset>
          <fieldset class="ease-parameter">
            <legend id="legend-p4" class="parameter-legend">P4</legend>
            <input data-parameters="p4" type="number" value="1.0" min="0" max="2" step="0.1"/>
            <input data-parameters="p4" type="range" value="1.0" min="0" max="2" step="0.1"/>
          </fieldset>
        </div>
      </div>
      <div id="eases-list" class="ease-visualizer-section"></div>
    </div>
  </div>
  <script type="module" src="index.js"></script>
</body>
</html>
