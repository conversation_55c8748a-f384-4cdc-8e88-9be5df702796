---
name: Bug report
about: Create a report to help us improve
title: "[BUG]"
labels: bug
assignees: ''

---

## Describe the bug

A clear and concise description of what the bug is.
If you're using an LLM to write the issue, please make sure that the text actually means something, and please, keep it short, LLMs tend to write longer texts than needed.
Do not post screenshots of your text editor. Quaote the code directly in the issue with [adequate code formatting](https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax#quoting-code).

## Provide a minimal reproduction demo of the bug

Use this [CodePen template](https://codepen.io/pen?template=pvoGoxR) or use a similar service to provide a minimal reproduction demo so I can quickly follow the steps to reproduce your problem.

## Detail the steps to reproduce the issue

For example:
1. In Chrome 138
2. Open this [demo](https://codepen.io/pen?template=pvoGoxR)
2. Click on '....'
3. Scroll down to '....'
4. See error

You can also add a screen recording of the bug in action if it's too complicated to describe.
